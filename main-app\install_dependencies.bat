@echo off
echo 📦 Instalowanie zależności Node.js dla Multi-Agent AI System
echo =============================================================
echo.

cd /d "%~dp0"

echo 📍 Katalog: %CD%
echo.

echo 🔍 Sprawdzanie Node.js...
"C:\Program Files\nodejs\node.exe" --version
if errorlevel 1 (
    echo ❌ Node.js nie jest dostępny
    pause
    exit /b 1
)

echo 🔍 Sprawdzanie NPM...
"C:\Program Files\nodejs\npm.cmd" --version
if errorlevel 1 (
    echo ❌ NPM nie jest dostępny
    pause
    exit /b 1
)

echo ✅ Node.js i NPM dostępne
echo.

echo 📦 Instalowanie zależności z package.json...
echo.
"C:\Program Files\nodejs\npm.cmd" install

if errorlevel 1 (
    echo.
    echo ❌ Błąd podczas instalacji zależności
    echo 💡 Sprawdź połączenie internetowe i spróbuj ponownie
    pause
    exit /b 1
)

echo.
echo ✅ Wszystkie zależności zainstalowane pomyślnie!
echo.
echo 📋 Zainstalowane pakiety:
echo    - @modelcontextprotocol/sdk (MCP SDK)
echo    - express (Web server)
echo    - winston (Logging)
echo    - dotenv (Environment variables)
echo    - uuid (UUID generation)
echo    - axios (HTTP client)
echo    - ws (WebSocket)
echo    - joi (Validation)
echo    - lodash (Utilities)
echo.
echo 🚀 System gotowy do uruchomienia!
echo.
pause