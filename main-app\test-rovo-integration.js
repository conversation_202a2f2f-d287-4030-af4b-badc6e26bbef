#!/usr/bin/env node

/**
 * Test integracji RovoDev
 */

import { RovoDevIntegration } from './src/integrations/RovoDevIntegration.js';
import { logger } from './src/utils/logger.js';
import dotenv from 'dotenv';

dotenv.config();

async function testRovoDevIntegration() {
    logger.info('🤖 Test integracji RovoDev...');

    try {
        const rovoDevIntegration = new RovoDevIntegration();
        
        // Inicjalizacja
        await rovoDevIntegration.initialize();
        
        // Sprawdź status
        const status = rovoDevIntegration.getStatus();
        logger.info('📊 Status RovoDev:', JSON.stringify(status, null, 2));
        
        // Test analizy kodu
        logger.info('🔍 Test analizy kodu...');
        const testCode = `
function calculateSum(a, b) {
    return a + b;
}

const result = calculateSum(5, 3);
console.log(result);
        `;
        
        const analysis = await rovoDevIntegration.analyzeCode(testCode, 'javascript');
        logger.info('📋 Wynik analizy:', JSON.stringify(analysis, null, 2));
        
        // Test generowania kodu
        logger.info('🔧 Test generowania kodu...');
        const generation = await rovoDevIntegration.generateCode(
            'Create a React component for user profile display',
            'javascript',
            { framework: 'react', includeTests: true }
        );
        logger.info('💻 Wygenerowany kod:', generation.success ? 'Sukces' : 'Fallback');
        if (generation.code) {
            logger.info('📝 Fragment kodu:', generation.code.substring(0, 200) + '...');
        }
        
        // Test code review
        logger.info('👀 Test code review...');
        const review = await rovoDevIntegration.reviewCode(testCode, 'javascript');
        logger.info('🔍 Wynik review:', JSON.stringify(review, null, 2));
        
        // Test debugowania
        logger.info('🐛 Test debugowania...');
        const debugging = await rovoDevIntegration.debugCode(
            testCode,
            new Error('Test error message')
        );
        logger.info('🔧 Wynik debugowania:', JSON.stringify(debugging, null, 2));
        
        // Test dokumentacji
        logger.info('📚 Test generowania dokumentacji...');
        const documentation = await rovoDevIntegration.generateDocumentation(testCode, 'javascript');
        logger.info('📖 Wygenerowana dokumentacja:', documentation.success ? 'Sukces' : 'Fallback');
        if (documentation.documentation) {
            logger.info('📝 Fragment dokumentacji:', documentation.documentation.substring(0, 200) + '...');
        }
        
        // Podsumowanie możliwości
        logger.info('🎯 Możliwości RovoDev:');
        const capabilities = rovoDevIntegration.getCapabilities();
        capabilities.forEach(capability => {
            logger.info(`  - ${capability}`);
        });
        
        logger.info('🎉 Test integracji RovoDev zakończony!');
        
    } catch (error) {
        logger.error('❌ Błąd podczas testu RovoDev:', error);
        process.exit(1);
    }
}

// Obsługa sygnałów
process.on('SIGINT', () => {
    logger.info('🛑 Przerwanie testu...');
    process.exit(0);
});

testRovoDevIntegration();