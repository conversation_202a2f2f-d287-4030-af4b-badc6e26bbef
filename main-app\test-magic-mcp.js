#!/usr/bin/env node

/**
 * Test serwera @21st-dev/magic MCP
 */

import { MCPManager } from './src/mcp/MCPManager.js';
import { logger } from './src/utils/logger.js';
import dotenv from 'dotenv';

dotenv.config();

async function testMagicMCP() {
    logger.info('🪄 Test serwera @21st-dev/magic MCP...');

    try {
        const mcpManager = new MCPManager();
        
        // Inicjalizacja
        await mcpManager.initialize();
        
        // Sprawdź status
        const status = mcpManager.getStatus();
        logger.info('📊 Status MCP Manager:', JSON.stringify(status, null, 2));
        
        // Sprawdź czy serwer magic jest dostępny
        const magicServerConnected = status.servers['21st-dev-magic']?.status === 'connected';
        
        if (magicServerConnected) {
            logger.info('✅ Serwer @21st-dev/magic jest połączony!');
            
            // Pobierz dostępne narzędzia
            const tools = mcpManager.getAllAvailableTools();
            const magicTools = tools.filter(tool => tool.serverId === '21st-dev-magic');
            
            logger.info(`🔧 Dostępne narzędzia Magic (${magicTools.length}):`);
            magicTools.forEach(tool => {
                logger.info(`  - ${tool.name}: ${tool.description || 'Brak opisu'}`);
            });
            
            // Test wywołania narzędzia (jeśli dostępne)
            if (magicTools.length > 0) {
                try {
                    const firstTool = magicTools[0];
                    logger.info(`🧪 Test narzędzia: ${firstTool.name}`);
                    
                    // Przykładowe wywołanie (dostosuj parametry do konkretnego narzędzia)
                    const result = await mcpManager.callTool('21st-dev-magic', firstTool.name, {
                        // Dodaj odpowiednie parametry dla narzędzia
                    });
                    
                    logger.info('✅ Wynik wywołania narzędzia:', result);
                } catch (toolError) {
                    logger.warn('⚠️ Błąd wywołania narzędzia (może wymagać parametrów):', toolError.message);
                }
            }
            
        } else {
            logger.warn('⚠️ Serwer @21st-dev/magic nie jest połączony');
            logger.info('💡 Sprawdź czy:');
            logger.info('  - Masz zainstalowany Node.js');
            logger.info('  - Masz dostęp do internetu (npx pobiera pakiet)');
            logger.info('  - API key jest poprawny');
        }
        
        // Cleanup
        await mcpManager.shutdown();
        
        logger.info('🎉 Test zakończony!');
        
    } catch (error) {
        logger.error('❌ Błąd podczas testu:', error);
        process.exit(1);
    }
}

// Obsługa sygnałów
process.on('SIGINT', () => {
    logger.info('🛑 Przerwanie testu...');
    process.exit(0);
});

testMagicMCP();