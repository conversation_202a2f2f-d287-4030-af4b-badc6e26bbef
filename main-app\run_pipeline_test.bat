@echo off
echo 🚀 Uruchamianie testu pipeline Multi-Agent AI System
echo.

cd /d "%~dp0"

echo 📋 Sprawdzanie Node.js...
"C:\Program Files\nodejs\node.exe" --version
if errorlevel 1 (
    echo ❌ Błąd: Node.js nie jest dostępny
    pause
    exit /b 1
)

echo ✅ Node.js dostępny
echo.

echo 📦 Sprawdzanie zależności...
if not exist "node_modules" (
    echo 📥 Instalowanie zależności...
    "C:\Program Files\nodejs\npm.exe" install
)

echo 🔄 Uruchamianie uproszczonego testu pipeline...
echo.
"C:\Program Files\nodejs\node.exe" tmp_rovodev_simple_pipeline_test.js

echo.
echo ✨ Test zakończony
pause