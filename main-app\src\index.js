#!/usr/bin/env node

import { SuperAgent } from './core/SuperAgent.js';
import { AgentRegistry } from './core/AgentRegistry.js';
import { TaskManager } from './core/TaskManager.js';
import { MCPManager } from './mcp/MCPManager.js';
import { AtlassianIntegration } from './integrations/AtlassianIntegration.js';
import { RovoDevIntegration } from './integrations/RovoDevIntegration.js';
import { logger } from './utils/logger.js';
import dotenv from 'dotenv';

// Załaduj konfigurację
dotenv.config();

async function main() {
    try {
        logger.info('🚀 Uruchamianie Multi-Agent AI System...');

        // Inicjalizacja komponentów
        const mcpManager = new MCPManager();
        const atlassianIntegration = new AtlassianIntegration();
        const rovoDevIntegration = new RovoDevIntegration();
        const agentRegistry = new AgentRegistry();
        const taskManager = new TaskManager();
        
        // Inicjalizacja SuperAgent
        const superAgent = new SuperAgent({
            mcpManager,
            atlassianIntegration,
            rovoDevIntegration,
            agentRegistry,
            taskManager
        });

        // Rejestracja agentów
        await agentRegistry.registerAgents();
        
        // Inicjalizacja Task Manager
        await taskManager.initialize();
        
        // Inicjalizacja RovoDev
        await rovoDevIntegration.initialize();
        
        // Uruchomienie systemu
        await superAgent.initialize();
        
        logger.info('✅ System Multi-Agent AI uruchomiony pomyślnie!');
        
        // Przykładowe zadania
        const tasks = [
            {
                id: 'task-001',
                title: 'Komponent React - Lista użytkowników',
                description: 'Stwórz komponent React do wyświetlania listy użytkowników',
                priority: 'high',
                type: 'development',
                requirements: [
                    'Użyj TypeScript',
                    'Dodaj paginację',
                    'Implementuj wyszukiwanie',
                    'Dodaj testy jednostkowe'
                ]
            },
            {
                id: 'task-002',
                title: 'Dokumentacja API',
                description: 'Stwórz dokumentację dla REST API',
                priority: 'medium',
                type: 'documentation',
                dependencies: ['task-001'],
                requirements: [
                    'Użyj OpenAPI 3.0',
                    'Dodaj przykłady',
                    'Opisz wszystkie endpointy'
                ]
            },
            {
                id: 'task-003',
                title: 'Testy integracyjne',
                description: 'Napisz testy integracyjne dla API',
                priority: 'high',
                type: 'testing',
                dependencies: ['task-001'],
                requirements: [
                    'Użyj Jest',
                    'Pokrycie min. 80%',
                    'Testy E2E'
                ]
            }
        ];

        // Utwórz zadania przez TaskManager
        for (const taskData of tasks) {
            const taskId = await superAgent.createTask(taskData);
            logger.info(`📋 Utworzono zadanie: ${taskData.title} (${taskId})`);
        }

        // Pokaż status systemu
        const systemStatus = superAgent.getSystemStatus();
        logger.info('📊 Status systemu:', JSON.stringify(systemStatus, null, 2));

    } catch (error) {
        logger.error('❌ Błąd podczas uruchamiania systemu:', error);
        process.exit(1);
    }
}

// Obsługa sygnałów
process.on('SIGINT', () => {
    logger.info('🛑 Zatrzymywanie systemu...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    logger.info('🛑 Zatrzymywanie systemu...');
    process.exit(0);
});

main();