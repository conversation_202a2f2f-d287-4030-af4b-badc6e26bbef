# Multi-Agent AI System with SuperAgent Orchestrator

## Architektura Systemu

### SuperAgent AI (Orchestrator)
Główny koordynator zarządzający przepływem pracy między agentami specjalistycznymi.

### Agenci Specjalistyczni:

1. **Planning Agent** - Planowanie wstępne zadań
2. **Research Agent** - Badania, analiza zasobów, dokumentacji
3. **Analysis Agent** - Podsumowanie i przekazanie instrukcji
4. **Coding Agent** - Implementacja kodu

### Narzędzia i Integracje:
- **MCP (Model Context Protocol)** - Komunikacja między agentami
- **MCP Servers** - Serwery obsługujące różne domeny
- **Atlassian CLI** - Integracja z Jira/Confluence
- **RovoDev** - Narzędzia deweloperskie

## Struktura Projektu

```
/
├── agents/                 # Definicje agentów
├── mcp/                   # Konfiguracja MCP
├── tools/                 # Narzędzia i integracje
├── workflows/             # Definicje przepływów pracy
├── config/               # Konfiguracja systemu
└── docs/                 # Dokumentacja
```