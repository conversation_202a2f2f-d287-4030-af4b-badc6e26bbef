import { BaseAgent } from './BaseAgent.js';

/**
 * Planning Agent - Agent odpowiedzialny za wstępne planowanie zadań
 */
export class PlanningAgent extends BaseAgent {
    constructor(config) {
        super({
            ...config,
            capabilities: [
                'task_decomposition',
                'requirement_analysis',
                'timeline_estimation',
                'resource_planning',
                'risk_assessment'
            ]
        });
    }

    async performTask(context) {
        const { task } = context;
        
        this.log('info', `Rozpoczynam planowanie zadania: ${task.description}`);

        // Analiza wymagań
        const requirements = await this.analyzeRequirements(task);
        
        // Dekompozycja zadania
        const subtasks = await this.decomposeTask(task, requirements);
        
        // Oszacowanie czasu
        const timeEstimation = await this.estimateTime(subtasks);
        
        // Identyfikacja zasobów
        const resources = await this.identifyResources(task, subtasks);
        
        // Ocena ryzyka
        const risks = await this.assessRisks(task, subtasks);

        const planningResult = {
            taskId: task.id,
            originalTask: task,
            requirements,
            subtasks,
            timeEstimation,
            resources,
            risks,
            recommendations: await this.generateRecommendations(task, subtasks, risks),
            createdAt: new Date()
        };

        this.log('info', `Zakończono planowanie. Zidentyfikowano ${subtasks.length} podzadań`);
        
        return planningResult;
    }

    async analyzeRequirements(task) {
        this.log('info', 'Analizuję wymagania zadania...');
        
        const requirements = {
            functional: [],
            nonFunctional: [],
            technical: [],
            business: []
        };

        // Analiza opisu zadania
        if (task.description) {
            // Identyfikacja wymagań funkcjonalnych
            if (task.description.includes('komponent') || task.description.includes('funkcja')) {
                requirements.functional.push('Implementacja komponentu/funkcji');
            }
            
            if (task.description.includes('test')) {
                requirements.functional.push('Implementacja testów');
            }
        }

        // Analiza wymagań z listy
        if (task.requirements) {
            task.requirements.forEach(req => {
                if (req.includes('TypeScript')) {
                    requirements.technical.push('Użycie TypeScript');
                }
                if (req.includes('test')) {
                    requirements.technical.push('Pokrycie testami');
                }
                if (req.includes('paginacja')) {
                    requirements.functional.push('Implementacja paginacji');
                }
                if (req.includes('wyszukiwanie')) {
                    requirements.functional.push('Implementacja wyszukiwania');
                }
            });
        }

        // Wymagania niefunkcjonalne
        requirements.nonFunctional.push('Wydajność');
        requirements.nonFunctional.push('Użyteczność');
        requirements.nonFunctional.push('Maintainability');

        return requirements;
    }

    async decomposeTask(task, requirements) {
        this.log('info', 'Dekompozycja zadania na podzadania...');
        
        const subtasks = [];

        // Podstawowe podzadania dla komponentu React
        if (task.description.includes('komponent React')) {
            subtasks.push({
                id: 'setup',
                name: 'Konfiguracja środowiska',
                description: 'Przygotowanie struktury plików i konfiguracji',
                priority: 'high',
                estimatedTime: '30min',
                dependencies: []
            });

            subtasks.push({
                id: 'component-structure',
                name: 'Struktura komponentu',
                description: 'Stworzenie podstawowej struktury komponentu',
                priority: 'high',
                estimatedTime: '1h',
                dependencies: ['setup']
            });

            if (requirements.functional.includes('Implementacja paginacji')) {
                subtasks.push({
                    id: 'pagination',
                    name: 'Implementacja paginacji',
                    description: 'Dodanie funkcjonalności paginacji',
                    priority: 'medium',
                    estimatedTime: '45min',
                    dependencies: ['component-structure']
                });
            }

            if (requirements.functional.includes('Implementacja wyszukiwania')) {
                subtasks.push({
                    id: 'search',
                    name: 'Implementacja wyszukiwania',
                    description: 'Dodanie funkcjonalności wyszukiwania',
                    priority: 'medium',
                    estimatedTime: '45min',
                    dependencies: ['component-structure']
                });
            }

            if (requirements.technical.includes('Pokrycie testami')) {
                subtasks.push({
                    id: 'testing',
                    name: 'Implementacja testów',
                    description: 'Stworzenie testów jednostkowych',
                    priority: 'high',
                    estimatedTime: '1h',
                    dependencies: ['component-structure', 'pagination', 'search']
                });
            }

            subtasks.push({
                id: 'documentation',
                name: 'Dokumentacja',
                description: 'Stworzenie dokumentacji komponentu',
                priority: 'low',
                estimatedTime: '30min',
                dependencies: ['testing']
            });
        }

        return subtasks;
    }

    async estimateTime(subtasks) {
        const totalMinutes = subtasks.reduce((total, subtask) => {
            const time = subtask.estimatedTime;
            let minutes = 0;
            
            if (time.includes('h')) {
                minutes += parseInt(time) * 60;
            }
            if (time.includes('min')) {
                minutes += parseInt(time.match(/(\d+)min/)?.[1] || 0);
            }
            
            return total + minutes;
        }, 0);

        return {
            totalMinutes,
            totalHours: Math.round(totalMinutes / 60 * 10) / 10,
            breakdown: subtasks.map(st => ({
                id: st.id,
                name: st.name,
                estimatedTime: st.estimatedTime
            }))
        };
    }

    async identifyResources(task, subtasks) {
        return {
            technologies: ['React', 'TypeScript', 'Jest', 'CSS/SCSS'],
            tools: ['VS Code', 'npm/yarn', 'Git'],
            frameworks: ['React Testing Library'],
            documentation: ['React Docs', 'TypeScript Handbook'],
            apis: []
        };
    }

    async assessRisks(task, subtasks) {
        return [
            {
                type: 'technical',
                description: 'Kompatybilność TypeScript z React',
                probability: 'low',
                impact: 'medium',
                mitigation: 'Użycie sprawdzonych typów React'
            },
            {
                type: 'timeline',
                description: 'Przekroczenie czasu na implementację testów',
                probability: 'medium',
                impact: 'low',
                mitigation: 'Priorytetyzacja podstawowych testów'
            }
        ];
    }

    async generateRecommendations(task, subtasks, risks) {
        return [
            'Rozpocznij od stworzenia podstawowej struktury komponentu',
            'Implementuj funkcjonalności w kolejności: podstawowy komponent → paginacja → wyszukiwanie → testy',
            'Użyj React Hooks dla zarządzania stanem',
            'Zastosuj TypeScript strict mode dla lepszej kontroli typów',
            'Stwórz testy dla każdej głównej funkcjonalności'
        ];
    }
}