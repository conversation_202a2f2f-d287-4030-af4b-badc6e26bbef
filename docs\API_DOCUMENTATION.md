# 📚 API Documentation - Multi-Agent AI System

## 🏗️ Architektura Systemu

### Główne Komponenty

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SuperAgent    │◄──►│   TaskManager   │◄──►│   MCPManager    │
│  (Orchestrator) │    │  (Zarząd<PERSON><PERSON>   │    │  (Inte<PERSON><PERSON><PERSON>    │
│                 │    │   zadaniami)    │    │     MCP)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  AgentRegistry  │    │  Event System   │    │  Integrations   │
│   (<PERSON><PERSON><PERSON><PERSON>      │    │  (Komunik<PERSON><PERSON>   │    │  (RovoDev,      │
│   agentów)      │    │   event-driven) │    │   Atlassian)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🤖 SuperAgent API

### Inicjalizacja

```javascript
import { SuperAgent } from './core/SuperAgent.js';

const superAgent = new SuperAgent({
    mcpManager,
    atlassianIntegration,
    rovoDevIntegration,
    agentRegistry,
    taskManager
});

await superAgent.initialize();
```

### Metody Główne

#### `createTask(taskData)`
Tworzy nowe zadanie w systemie.

**Parametry:**
```javascript
{
    id: string,              // Unikalny identyfikator (opcjonalny)
    title: string,           // Tytuł zadania
    description: string,     // Opis zadania
    priority: 'high'|'medium'|'low',  // Priorytet
    type: string,           // Typ zadania (development, documentation, testing)
    dependencies: string[], // Lista ID zadań, od których zależy
    requirements: string[]  // Lista wymagań
}
```

**Przykład:**
```javascript
const taskId = await superAgent.createTask({
    title: 'Komponent React - Dashboard',
    description: 'Stwórz komponent dashboard z wykresami',
    priority: 'high',
    type: 'development',
    dependencies: ['task-001'],
    requirements: [
        'Użyj TypeScript',
        'Dodaj wykresy Chart.js',
        'Responsive design'
    ]
});
```

#### `executeTask(taskId, agentType)`
Wykonuje zadanie przez określonego agenta.

```javascript
const result = await superAgent.executeTask('task-001', 'coding');
```

#### `getSystemStatus()`
Zwraca status całego systemu.

```javascript
const status = superAgent.getSystemStatus();
// Zwraca: { agents, tasks, mcp, atlassian, rovo }
```

## 📋 TaskManager API

### Zarządzanie Zadaniami

#### `addTask(task)`
Dodaje zadanie do kolejki.

```javascript
await taskManager.addTask({
    id: 'custom-task-001',
    title: 'Analiza wydajności',
    priority: 'medium',
    agent: 'analysis',
    data: { metrics: ['cpu', 'memory', 'network'] }
});
```

#### `getTask(taskId)`
Pobiera zadanie po ID.

```javascript
const task = await taskManager.getTask('task-001');
```

#### `updateTaskStatus(taskId, status, result)`
Aktualizuje status zadania.

```javascript
await taskManager.updateTaskStatus('task-001', 'completed', {
    output: 'Komponent został utworzony',
    files: ['src/components/Dashboard.tsx']
});
```

#### `getTasksByStatus(status)`
Pobiera zadania według statusu.

```javascript
const pendingTasks = await taskManager.getTasksByStatus('pending');
const runningTasks = await taskManager.getTasksByStatus('running');
```

### Monitoring i Metryki

#### `getStatus()`
Zwraca status TaskManager.

```javascript
const status = taskManager.getStatus();
// Zwraca:
{
    total: 10,
    pending: 3,
    running: 2,
    completed: 4,
    failed: 1,
    maxConcurrent: 3
}
```

#### `getMetrics()`
Zwraca metryki wydajności.

```javascript
const metrics = taskManager.getMetrics();
// Zwraca:
{
    averageExecutionTime: 45000,  // ms
    successRate: 0.85,
    totalProcessed: 100,
    failureReasons: {
        'timeout': 5,
        'error': 3
    }
}
```

## 🔌 MCPManager API

### Zarządzanie Serwerami

#### `addServer(name, config)`
Dodaje nowy serwer MCP.

```javascript
await mcpManager.addServer('custom-server', {
    command: 'node',
    args: ['./custom-mcp-server.js'],
    env: { API_KEY: 'your-key' }
});
```

#### `callTool(serverName, toolName, args)`
Wywołuje narzędzie na serwerze MCP.

```javascript
const result = await mcpManager.callTool('local-tools', 'read_file', {
    path: './src/index.js'
});
```

#### `getAllAvailableTools()`
Pobiera wszystkie dostępne narzędzia.

```javascript
const tools = mcpManager.getAllAvailableTools();
// Zwraca mapę: serverName -> [tools]
```

### Dostępne Serwery MCP

#### Local Tools Server
```javascript
// Dostępne narzędzia:
- read_file(path)           // Odczyt pliku
- write_file(path, content) // Zapis pliku
- list_directory(path)      // Lista plików
- get_system_info()         // Info o systemie
- execute_command(command)  // Wykonanie polecenia
```

#### @21st-dev/magic Server
```javascript
// Narzędzia AI:
- generate_code(prompt, language)
- analyze_code(code, language)
- suggest_improvements(code)
- generate_tests(code)
```

#### GitHub Server
```javascript
// Integracja GitHub:
- create_repository(name, description)
- get_repository(owner, repo)
- create_issue(owner, repo, title, body)
- list_pull_requests(owner, repo)
```

## 🤖 Agenci Specjalistyczni

### BaseAgent

Klasa bazowa dla wszystkich agentów.

```javascript
class CustomAgent extends BaseAgent {
    constructor() {
        super('custom', ['analysis', 'reporting']);
    }

    async execute(task) {
        // Implementacja logiki agenta
        return {
            success: true,
            result: 'Zadanie wykonane',
            output: {}
        };
    }
}
```

### PlanningAgent

Specjalizuje się w planowaniu zadań.

```javascript
const result = await planningAgent.execute({
    type: 'project_planning',
    data: {
        requirements: ['Feature A', 'Feature B'],
        timeline: '2 weeks',
        resources: ['2 developers', '1 designer']
    }
});
```

### CodingAgent

Agent do zadań programistycznych z integracją RovoDev.

```javascript
const result = await codingAgent.execute({
    type: 'code_generation',
    data: {
        language: 'typescript',
        framework: 'react',
        requirements: ['Component with props', 'TypeScript interfaces']
    }
});
```

## 🔗 Integracje

### RovoDev Integration

```javascript
// AI-assisted coding
const codeAnalysis = await rovoDevIntegration.analyzeCode(sourceCode);
const suggestions = await rovoDevIntegration.getSuggestions(context);
const generatedCode = await rovoDevIntegration.generateCode(prompt);
```

### Atlassian Integration

```javascript
// Jira integration
const issue = await atlassianIntegration.createJiraIssue({
    project: 'PROJ',
    summary: 'New feature request',
    description: 'Detailed description',
    issueType: 'Story'
});

// Confluence integration
const page = await atlassianIntegration.createConfluencePage({
    space: 'DEV',
    title: 'API Documentation',
    content: markdownContent
});
```

## 📊 Event System

System używa EventEmitter do komunikacji między komponentami.

### Dostępne Eventy

```javascript
// TaskManager events
taskManager.on('task:created', (task) => {});
taskManager.on('task:started', (task) => {});
taskManager.on('task:completed', (task, result) => {});
taskManager.on('task:failed', (task, error) => {});

// SuperAgent events
superAgent.on('agent:registered', (agent) => {});
superAgent.on('workflow:started', (workflow) => {});
superAgent.on('system:ready', () => {});

// MCPManager events
mcpManager.on('server:connected', (serverName) => {});
mcpManager.on('server:disconnected', (serverName) => {});
mcpManager.on('tool:called', (serverName, toolName, args) => {});
```

## 🛠️ Konfiguracja

### Zmienne Środowiskowe

```bash
# .env file
NODE_ENV=development
LOG_LEVEL=info

# MCP Servers
CONTEXT7_API_KEY=your_context7_key
TWENTYFIRST_API_KEY=your_21st_dev_key

# Atlassian
ATLASSIAN_API_TOKEN=your_atlassian_token
ATLASSIAN_EMAIL=your_email
ATLASSIAN_DOMAIN=your_domain

# RovoDev
ROVO_DEV_API_KEY=your_rovo_key
```

### Konfiguracja MCP

```json
// mcp-config.json
{
  "servers": {
    "local-tools": {
      "command": "node",
      "args": ["src/mcp/servers/local-tools-server.js"]
    },
    "@21st-dev/magic": {
      "command": "npx",
      "args": ["-y", "@21st-dev/magic"],
      "env": {
        "MAGIC_API_KEY": "sk-magic-demo-key"
      }
    }
  }
}
```

## 🔍 Debugging i Logowanie

### Logger Configuration

```javascript
import { logger } from './utils/logger.js';

// Różne poziomy logowania
logger.error('Błąd krytyczny', error);
logger.warn('Ostrzeżenie');
logger.info('Informacja');
logger.debug('Debug info');

// Logowanie z kontekstem
logger.info('Zadanie utworzone', { 
    taskId: 'task-001', 
    agent: 'coding',
    priority: 'high' 
});
```

### Monitoring Wydajności

```javascript
// Metryki systemu
const systemMetrics = {
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    tasks: taskManager.getMetrics(),
    mcp: mcpManager.getConnectionStatus()
};
```

## 🚀 Przykłady Użycia

### Kompletny Workflow

```javascript
// 1. Inicjalizacja systemu
const system = await initializeSystem();

// 2. Utworzenie zadań z zależnościami
const designTask = await system.createTask({
    title: 'UI Design',
    type: 'design',
    priority: 'high'
});

const codeTask = await system.createTask({
    title: 'Implementation',
    type: 'development',
    priority: 'high',
    dependencies: [designTask]
});

const testTask = await system.createTask({
    title: 'Testing',
    type: 'testing',
    priority: 'medium',
    dependencies: [codeTask]
});

// 3. Monitoring postępu
system.on('task:completed', (task) => {
    console.log(`Zadanie ${task.title} ukończone!`);
});

// 4. Sprawdzanie statusu
const status = system.getSystemStatus();
console.log('Status systemu:', status);
```

### Integracja z Zewnętrznymi API

```javascript
// Użycie MCP do integracji z GitHub
const repoInfo = await mcpManager.callTool('github', 'get_repository', {
    owner: 'username',
    repo: 'project-name'
});

// Utworzenie issue na podstawie zadania
const issue = await mcpManager.callTool('github', 'create_issue', {
    owner: 'username',
    repo: 'project-name',
    title: task.title,
    body: task.description
});
```

## 📈 Rozszerzanie Systemu

### Dodawanie Nowego Agenta

```javascript
import { BaseAgent } from './core/BaseAgent.js';

class DataAnalysisAgent extends BaseAgent {
    constructor() {
        super('data-analysis', ['analytics', 'reporting', 'visualization']);
    }

    async execute(task) {
        // Implementacja analizy danych
        const data = await this.loadData(task.data.source);
        const analysis = await this.analyzeData(data);
        const report = await this.generateReport(analysis);
        
        return {
            success: true,
            result: 'Analiza ukończona',
            output: { report, charts: analysis.charts }
        };
    }
}

// Rejestracja agenta
agentRegistry.register(new DataAnalysisAgent());
```

### Dodawanie Nowego Serwera MCP

```javascript
// Konfiguracja nowego serwera
await mcpManager.addServer('custom-analytics', {
    command: 'python',
    args: ['analytics-server.py'],
    env: {
        ANALYTICS_API_KEY: process.env.ANALYTICS_KEY
    }
});
```

Ta dokumentacja API zapewnia kompletny przewodnik po wszystkich funkcjach systemu Multi-Agent AI.