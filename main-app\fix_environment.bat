@echo off
echo ========================================
echo   NAPRAWA ŚRODOWISKA - Multi-Agent AI
echo ========================================
echo.

echo [1/5] Sprawdzanie Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js nie jest zainstalowany!
    echo.
    echo Opcje instalacji:
    echo 1. <PERSON>bierz z https://nodejs.org/
    echo 2. Użyj winget: winget install OpenJS.NodeJS
    echo 3. Użyj chocolatey: choco install nodejs
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js jest zainstalowany
    node --version
)

echo.
echo [2/5] Sprawdzanie npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm nie jest dostępny!
    exit /b 1
) else (
    echo ✅ npm jest dostępny
    npm --version
)

echo.
echo [3/5] Instalowanie zależności...
npm install
if %errorlevel% neq 0 (
    echo ❌ Błąd instalacji zależności!
    exit /b 1
) else (
    echo ✅ Zależności zainstalowane
)

echo.
echo [4/5] Konfiguracja środowiska...
if not exist .env (
    if exist .env.example (
        copy .env.example .env
        echo ✅ Skopiowano .env.example do .env
        echo ⚠️  Skonfiguruj zmienne środowiskowe w pliku .env
    ) else (
        echo ❌ Brak pliku .env.example
    )
) else (
    echo ✅ Plik .env już istnieje
)

echo.
echo [5/5] Tworzenie katalogów...
if not exist logs mkdir logs
if not exist data mkdir data
if not exist data\tasks mkdir data\tasks
echo ✅ Katalogi utworzone

echo.
echo ========================================
echo   ŚRODOWISKO NAPRAWIONE!
echo ========================================
echo.
echo Następne kroki:
echo 1. Skonfiguruj zmienne w pliku .env
echo 2. Uruchom: npm run test:system
echo 3. Uruchom: npm start
echo.
pause