import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import path from 'path';

/**
 * Manager zarząd<PERSON>ją<PERSON> zadaniami w systemie
 */
export class TaskManager extends EventEmitter {
    constructor({ persistencePath = './data/tasks' } = {}) {
        super();
        this.tasks = new Map();
        this.taskQueue = [];
        this.activeTasks = new Map();
        this.completedTasks = new Map();
        this.failedTasks = new Map();
        this.persistencePath = persistencePath;
        this.maxConcurrentTasks = 3;
        this.retryAttempts = 3;
        this.isProcessing = false;
    }

    async initialize() {
        logger.info('📋 Inicjalizacja Task Manager...');
        
        try {
            // Utwórz katalog dla persystencji jeśli nie istnieje
            await this.ensurePersistenceDirectory();
            
            // Załaduj zapisane zadania
            await this.loadPersistedTasks();
            
            // Uruchom przetwarzanie kolejki
            this.startQueueProcessing();
            
            logger.info('✅ Task Manager zainicjalizowany pomyślnie');
        } catch (error) {
            logger.error('❌ Błąd inicjalizacji Task Manager:', error);
            throw error;
        }
    }

    async ensurePersistenceDirectory() {
        try {
            await fs.mkdir(this.persistencePath, { recursive: true });
        } catch (error) {
            if (error.code !== 'EEXIST') {
                throw error;
            }
        }
    }

    async loadPersistedTasks() {
        try {
            const tasksFile = path.join(this.persistencePath, 'tasks.json');
            const data = await fs.readFile(tasksFile, 'utf8');
            const persistedData = JSON.parse(data);
            
            // Przywróć zadania
            if (persistedData.tasks) {
                for (const [taskId, taskData] of Object.entries(persistedData.tasks)) {
                    this.tasks.set(taskId, {
                        ...taskData,
                        createdAt: new Date(taskData.createdAt),
                        updatedAt: new Date(taskData.updatedAt)
                    });
                }
            }
            
            // Przywróć kolejkę (tylko zadania pending)
            if (persistedData.queue) {
                this.taskQueue = persistedData.queue.filter(taskId => {
                    const task = this.tasks.get(taskId);
                    return task && task.status === 'pending';
                });
            }
            
            logger.info(`📂 Załadowano ${this.tasks.size} zadań z persystencji`);
        } catch (error) {
            if (error.code !== 'ENOENT') {
                logger.warn('⚠️ Nie udało się załadować zadań z persystencji:', error.message);
            }
        }
    }

    async persistTasks() {
        try {
            const tasksFile = path.join(this.persistencePath, 'tasks.json');
            const data = {
                tasks: Object.fromEntries(this.tasks),
                queue: this.taskQueue,
                lastSaved: new Date().toISOString()
            };
            
            await fs.writeFile(tasksFile, JSON.stringify(data, null, 2));
        } catch (error) {
            logger.error('❌ Błąd zapisywania zadań:', error);
        }
    }

    /**
     * Tworzy nowe zadanie
     */
    async createTask(taskData) {
        const taskId = taskData.id || uuidv4();
        
        const task = {
            id: taskId,
            type: taskData.type || 'general',
            title: taskData.title || taskData.description?.substring(0, 50) || 'Untitled Task',
            description: taskData.description || '',
            priority: taskData.priority || 'medium',
            status: 'pending',
            progress: 0,
            requirements: taskData.requirements || [],
            metadata: taskData.metadata || {},
            dependencies: taskData.dependencies || [],
            assignedAgents: taskData.assignedAgents || [],
            retryCount: 0,
            maxRetries: taskData.maxRetries || this.retryAttempts,
            createdAt: new Date(),
            updatedAt: new Date(),
            estimatedDuration: taskData.estimatedDuration || null,
            actualDuration: null,
            result: null,
            error: null
        };

        this.tasks.set(taskId, task);
        
        // Dodaj do kolejki jeśli nie ma zależności lub są spełnione
        if (await this.areDependenciesMet(task)) {
            this.addToQueue(taskId);
        }

        await this.persistTasks();
        
        this.emit('task:created', taskId, task);
        logger.info(`📋 Utworzono zadanie: ${task.title} (${taskId})`);
        
        return taskId;
    }

    /**
     * Dodaje zadanie do kolejki z priorytetyzacją
     */
    addToQueue(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) return;

        // Usuń z kolejki jeśli już istnieje
        this.taskQueue = this.taskQueue.filter(id => id !== taskId);
        
        // Dodaj z uwzględnieniem priorytetu
        const priorityOrder = { 'high': 0, 'medium': 1, 'low': 2 };
        const taskPriority = priorityOrder[task.priority] || 1;
        
        let insertIndex = this.taskQueue.length;
        for (let i = 0; i < this.taskQueue.length; i++) {
            const queuedTask = this.tasks.get(this.taskQueue[i]);
            const queuedPriority = priorityOrder[queuedTask?.priority] || 1;
            
            if (taskPriority < queuedPriority) {
                insertIndex = i;
                break;
            }
        }
        
        this.taskQueue.splice(insertIndex, 0, taskId);
        this.emit('task:queued', taskId);
    }

    /**
     * Sprawdza czy zależności zadania są spełnione
     */
    async areDependenciesMet(task) {
        if (!task.dependencies || task.dependencies.length === 0) {
            return true;
        }

        for (const depId of task.dependencies) {
            const depTask = this.tasks.get(depId);
            if (!depTask || depTask.status !== 'completed') {
                return false;
            }
        }

        return true;
    }

    /**
     * Uruchamia przetwarzanie kolejki zadań
     */
    startQueueProcessing() {
        if (this.isProcessing) return;
        
        this.isProcessing = true;
        this.processQueue();
    }

    async processQueue() {
        while (this.isProcessing) {
            try {
                // Sprawdź czy można uruchomić więcej zadań
                if (this.activeTasks.size >= this.maxConcurrentTasks || this.taskQueue.length === 0) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    continue;
                }

                // Pobierz następne zadanie z kolejki
                const taskId = this.taskQueue.shift();
                if (!taskId) continue;

                const task = this.tasks.get(taskId);
                if (!task || task.status !== 'pending') continue;

                // Sprawdź zależności ponownie
                if (!(await this.areDependenciesMet(task))) {
                    // Dodaj z powrotem na koniec kolejki
                    this.taskQueue.push(taskId);
                    continue;
                }

                // Uruchom zadanie
                this.executeTask(taskId);
                
            } catch (error) {
                logger.error('❌ Błąd w przetwarzaniu kolejki zadań:', error);
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }
    }

    /**
     * Wykonuje zadanie
     */
    async executeTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) return;

        try {
            // Oznacz jako aktywne
            task.status = 'running';
            task.startedAt = new Date();
            task.updatedAt = new Date();
            this.activeTasks.set(taskId, task);
            
            this.emit('task:started', taskId, task);
            logger.info(`🚀 Rozpoczęto wykonywanie zadania: ${task.title} (${taskId})`);

            // Symulacja wykonania - w rzeczywistości tutaj byłoby wywołanie SuperAgent
            const result = await this.simulateTaskExecution(task);
            
            // Oznacz jako zakończone
            task.status = 'completed';
            task.progress = 100;
            task.result = result;
            task.completedAt = new Date();
            task.actualDuration = task.completedAt - task.startedAt;
            task.updatedAt = new Date();
            
            this.activeTasks.delete(taskId);
            this.completedTasks.set(taskId, task);
            
            this.emit('task:completed', taskId, task, result);
            logger.info(`✅ Zakończono zadanie: ${task.title} (${taskId})`);
            
            // Sprawdź czy można uruchomić zadania zależne
            await this.checkDependentTasks(taskId);
            
        } catch (error) {
            await this.handleTaskError(taskId, error);
        }
        
        await this.persistTasks();
    }

    async simulateTaskExecution(task) {
        // Symulacja - w rzeczywistości tutaj byłoby wywołanie SuperAgent.executeTask()
        await new Promise(resolve => setTimeout(resolve, Math.random() * 3000 + 1000));
        
        return {
            taskId: task.id,
            status: 'completed',
            message: `Zadanie "${task.title}" zostało wykonane pomyślnie`,
            timestamp: new Date()
        };
    }

    async handleTaskError(taskId, error) {
        const task = this.tasks.get(taskId);
        if (!task) return;

        task.retryCount++;
        task.error = {
            message: error.message,
            stack: error.stack,
            timestamp: new Date()
        };
        task.updatedAt = new Date();

        this.activeTasks.delete(taskId);

        if (task.retryCount < task.maxRetries) {
            // Ponów próbę
            task.status = 'pending';
            this.addToQueue(taskId);
            
            this.emit('task:retry', taskId, task, task.retryCount);
            logger.warn(`🔄 Ponowienie próby zadania: ${task.title} (${task.retryCount}/${task.maxRetries})`);
        } else {
            // Oznacz jako nieudane
            task.status = 'failed';
            this.failedTasks.set(taskId, task);
            
            this.emit('task:failed', taskId, task, error);
            logger.error(`❌ Zadanie nieudane: ${task.title} (${taskId})`, error);
        }
    }

    async checkDependentTasks(completedTaskId) {
        for (const [taskId, task] of this.tasks) {
            if (task.status === 'pending' && 
                task.dependencies.includes(completedTaskId) &&
                await this.areDependenciesMet(task)) {
                this.addToQueue(taskId);
            }
        }
    }

    /**
     * Pobiera zadanie po ID
     */
    getTask(taskId) {
        return this.tasks.get(taskId);
    }

    /**
     * Pobiera status wszystkich zadań
     */
    getStatus() {
        return {
            total: this.tasks.size,
            pending: Array.from(this.tasks.values()).filter(t => t.status === 'pending').length,
            running: this.activeTasks.size,
            completed: this.completedTasks.size,
            failed: this.failedTasks.size,
            queued: this.taskQueue.length,
            maxConcurrent: this.maxConcurrentTasks
        };
    }

    /**
     * Zatrzymuje przetwarzanie kolejki
     */
    stop() {
        this.isProcessing = false;
        logger.info('🛑 Zatrzymano przetwarzanie kolejki zadań');
    }

    /**
     * Czyści zakończone zadania
     */
    async cleanup(olderThanDays = 7) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
        
        let cleanedCount = 0;
        
        for (const [taskId, task] of this.completedTasks) {
            if (task.completedAt < cutoffDate) {
                this.tasks.delete(taskId);
                this.completedTasks.delete(taskId);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            await this.persistTasks();
            logger.info(`🧹 Wyczyszczono ${cleanedCount} starych zadań`);
        }
    }
}