import { BaseAgent } from './BaseAgent.js';

/**
 * Research Agent - Agent odpowiedzialny za badania i analizę zasobów
 */
export class ResearchAgent extends BaseAgent {
    constructor(config) {
        super({
            ...config,
            capabilities: [
                'web_research',
                'documentation_analysis',
                'code_examples_search',
                'framework_analysis',
                'best_practices_research',
                'api_documentation_review'
            ]
        });
        
        // Baza wiedzy agenta
        this.knowledgeBase = {
            react: {
                documentation: 'https://react.dev/',
                bestPractices: [
                    'Użyj funkcyjnych komponentów z hooks',
                    'Zastosuj memo dla optymalizacji',
                    'Używaj TypeScript dla lepszej kontroli typów'
                ],
                patterns: ['Container/Presentational', 'Custom Hooks', 'Compound Components']
            },
            typescript: {
                documentation: 'https://www.typescriptlang.org/docs/',
                bestPractices: [
                    'Używaj strict mode',
                    'Definiuj interfejsy dla props',
                    'Wykorzystuj utility types'
                ]
            },
            testing: {
                frameworks: ['Jest', 'React Testing Library', 'Vitest'],
                patterns: ['AAA Pattern', 'Page Object Model', 'Test Doubles']
            }
        };
    }

    async performTask(context) {
        const { task, planningResult } = context;
        
        this.log('info', `Rozpoczynam research dla zadania: ${task.description}`);

        // Analiza wymagań technicznych
        const technicalResearch = await this.researchTechnicalRequirements(task, planningResult);
        
        // Wyszukiwanie przykładów kodu
        const codeExamples = await this.findCodeExamples(task, planningResult);
        
        // Analiza frameworków i bibliotek
        const frameworkAnalysis = await this.analyzeFrameworks(task, planningResult);
        
        // Badanie najlepszych praktyk
        const bestPractices = await this.researchBestPractices(task, planningResult);
        
        // Analiza dokumentacji API
        const apiDocumentation = await this.analyzeApiDocumentation(task, planningResult);
        
        // Identyfikacja potencjalnych problemów
        const potentialIssues = await this.identifyPotentialIssues(task, planningResult);

        const researchResult = {
            taskId: task.id,
            technicalResearch,
            codeExamples,
            frameworkAnalysis,
            bestPractices,
            apiDocumentation,
            potentialIssues,
            recommendations: await this.generateResearchRecommendations(task, planningResult),
            sources: this.getResearchSources(),
            createdAt: new Date()
        };

        this.log('info', `Zakończono research. Znaleziono ${codeExamples.length} przykładów kodu`);
        
        return researchResult;
    }

    async researchTechnicalRequirements(task, planningResult) {
        this.log('info', 'Badanie wymagań technicznych...');
        
        const requirements = planningResult.requirements;
        const research = {
            react: {},
            typescript: {},
            testing: {},
            styling: {},
            stateManagement: {}
        };

        // Research React
        if (requirements.technical.includes('Użycie TypeScript')) {
            research.react = {
                version: 'React 18+',
                features: ['Hooks', 'Functional Components', 'Concurrent Features'],
                typescriptIntegration: {
                    setup: '@types/react, @types/react-dom',
                    configuration: 'tsconfig.json with React settings',
                    bestPractices: this.knowledgeBase.react.bestPractices
                }
            };
        }

        // Research TypeScript
        research.typescript = {
            version: 'TypeScript 5.0+',
            configuration: {
                strict: true,
                jsx: 'react-jsx',
                moduleResolution: 'node'
            },
            reactTypes: ['FC', 'ReactNode', 'ComponentProps', 'PropsWithChildren']
        };

        // Research testowania
        if (requirements.technical.includes('Pokrycie testami')) {
            research.testing = {
                framework: 'Jest + React Testing Library',
                setup: '@testing-library/react, @testing-library/jest-dom',
                patterns: this.knowledgeBase.testing.patterns,
                coverage: 'Minimum 80% coverage recommended'
            };
        }

        return research;
    }

    async findCodeExamples(task, planningResult) {
        this.log('info', 'Wyszukiwanie przykładów kodu...');
        
        const examples = [];

        // Przykład podstawowego komponentu React z TypeScript
        if (task.description.includes('komponent React')) {
            examples.push({
                title: 'Podstawowy komponent React z TypeScript',
                description: 'Funkcyjny komponent z interfejsem props',
                code: `
interface UserListProps {
  users: User[];
  onUserSelect: (user: User) => void;
  loading?: boolean;
}

const UserList: React.FC<UserListProps> = ({ 
  users, 
  onUserSelect, 
  loading = false 
}) => {
  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="user-list">
      {users.map(user => (
        <UserItem 
          key={user.id} 
          user={user} 
          onClick={() => onUserSelect(user)} 
        />
      ))}
    </div>
  );
};`,
                source: 'React TypeScript Best Practices',
                tags: ['react', 'typescript', 'component']
            });
        }

        // Przykład paginacji
        if (planningResult.requirements.functional.includes('Implementacja paginacji')) {
            examples.push({
                title: 'Hook do paginacji',
                description: 'Custom hook zarządzający stanem paginacji',
                code: `
interface UsePaginationProps {
  totalItems: number;
  itemsPerPage: number;
  initialPage?: number;
}

const usePagination = ({ 
  totalItems, 
  itemsPerPage, 
  initialPage = 1 
}: UsePaginationProps) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };
  
  return {
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    goToPage,
    nextPage: () => goToPage(currentPage + 1),
    prevPage: () => goToPage(currentPage - 1),
    canGoNext: currentPage < totalPages,
    canGoPrev: currentPage > 1
  };
};`,
                source: 'React Hooks Patterns',
                tags: ['react', 'hooks', 'pagination']
            });
        }

        // Przykład wyszukiwania
        if (planningResult.requirements.functional.includes('Implementacja wyszukiwania')) {
            examples.push({
                title: 'Hook do wyszukiwania z debounce',
                description: 'Custom hook z debounced search',
                code: `
const useSearch = <T>(
  items: T[],
  searchFields: (keyof T)[],
  debounceMs = 300
) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedTerm, setDebouncedTerm] = useState('');
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, debounceMs);
    
    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);
  
  const filteredItems = useMemo(() => {
    if (!debouncedTerm) return items;
    
    return items.filter(item =>
      searchFields.some(field =>
        String(item[field])
          .toLowerCase()
          .includes(debouncedTerm.toLowerCase())
      )
    );
  }, [items, searchFields, debouncedTerm]);
  
  return {
    searchTerm,
    setSearchTerm,
    filteredItems,
    isSearching: searchTerm !== debouncedTerm
  };
};`,
                source: 'React Performance Patterns',
                tags: ['react', 'hooks', 'search', 'debounce']
            });
        }

        return examples;
    }

    async analyzeFrameworks(task, planningResult) {
        this.log('info', 'Analiza frameworków i bibliotek...');
        
        return {
            react: {
                version: '18.2.0',
                features: ['Concurrent Features', 'Automatic Batching', 'Suspense'],
                ecosystem: ['React Router', 'React Query', 'Zustand']
            },
            typescript: {
                version: '5.0+',
                features: ['Decorators', 'const assertions', 'Template literal types']
            },
            testing: {
                jest: {
                    version: '29.0+',
                    features: ['ESM support', 'Snapshot testing', 'Coverage reports']
                },
                reactTestingLibrary: {
                    version: '13.0+',
                    philosophy: 'Testing user behavior, not implementation'
                }
            },
            styling: {
                options: ['CSS Modules', 'Styled Components', 'Tailwind CSS', 'SCSS'],
                recommendation: 'CSS Modules for component-scoped styling'
            }
        };
    }

    async researchBestPractices(task, planningResult) {
        this.log('info', 'Badanie najlepszych praktyk...');
        
        return {
            react: [
                'Używaj funkcyjnych komponentów z hooks',
                'Zastosuj React.memo dla komponentów z częstymi re-renderami',
                'Używaj useCallback i useMemo dla optymalizacji',
                'Podziel duże komponenty na mniejsze',
                'Używaj custom hooks dla logiki biznesowej'
            ],
            typescript: [
                'Definiuj interfejsy dla wszystkich props',
                'Używaj union types zamiast any',
                'Wykorzystuj utility types (Pick, Omit, Partial)',
                'Używaj const assertions dla lepszej inferencji typów'
            ],
            testing: [
                'Testuj zachowanie, nie implementację',
                'Używaj data-testid dla elementów testowych',
                'Mockuj zewnętrzne zależności',
                'Pisz testy przed refaktoringiem'
            ],
            performance: [
                'Lazy loading dla dużych komponentów',
                'Virtualizacja dla długich list',
                'Debounce dla search inputs',
                'Memoization dla expensive calculations'
            ]
        };
    }

    async analyzeApiDocumentation(task, planningResult) {
        this.log('info', 'Analiza dokumentacji API...');
        
        return {
            react: {
                hooks: {
                    useState: 'Zarządzanie lokalnym stanem komponentu',
                    useEffect: 'Side effects i lifecycle',
                    useMemo: 'Memoization expensive calculations',
                    useCallback: 'Memoization funkcji',
                    useRef: 'Referencje do DOM i mutable values'
                }
            },
            reactTestingLibrary: {
                queries: ['getByRole', 'getByText', 'getByTestId'],
                events: ['fireEvent', 'userEvent'],
                async: ['waitFor', 'findBy*']
            }
        };
    }

    async identifyPotentialIssues(task, planningResult) {
        this.log('info', 'Identyfikacja potencjalnych problemów...');
        
        return [
            {
                category: 'Performance',
                issue: 'Re-rendering przy każdej zmianie search term',
                solution: 'Użyj debounce i useMemo'
            },
            {
                category: 'TypeScript',
                issue: 'Brak typów dla external APIs',
                solution: 'Stwórz interfejsy dla API responses'
            },
            {
                category: 'Testing',
                issue: 'Trudność w testowaniu async operations',
                solution: 'Użyj waitFor i proper mocking'
            },
            {
                category: 'Accessibility',
                issue: 'Brak proper ARIA labels',
                solution: 'Dodaj aria-label i role attributes'
            }
        ];
    }

    async generateResearchRecommendations(task, planningResult) {
        return [
            'Użyj React 18 z TypeScript w strict mode',
            'Implementuj custom hooks dla paginacji i wyszukiwania',
            'Zastosuj React Testing Library dla testów',
            'Użyj CSS Modules dla stylowania',
            'Implementuj proper error boundaries',
            'Dodaj loading states i error handling',
            'Zastosuj debounce dla search functionality',
            'Użyj React.memo dla optymalizacji performance'
        ];
    }

    getResearchSources() {
        return [
            'React Official Documentation',
            'TypeScript Handbook',
            'React Testing Library Docs',
            'React Patterns Repository',
            'TypeScript React Cheatsheet',
            'Jest Documentation',
            'MDN Web Docs'
        ];
    }
}