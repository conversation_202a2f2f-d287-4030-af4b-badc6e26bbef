import winston from 'winston';

// Konfiguracja formatów logów
const logFormat = winston.format.combine(
    winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, stack }) => {
        return `${timestamp} [${level.toUpperCase()}]: ${stack || message}`;
    })
);

// Konfiguracja kolorów dla konsoli
const consoleFormat = winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp({
        format: 'HH:mm:ss'
    }),
    winston.format.printf(({ level, message, timestamp }) => {
        return `${timestamp} ${level}: ${message}`;
    })
);

// Tworzenie loggera
export const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    defaultMeta: { service: 'multi-agent-ai' },
    transports: [
        // Konsola
        new winston.transports.Console({
            format: consoleFormat
        }),
        
        // Plik z błędami
        new winston.transports.File({
            filename: 'logs/error.log',
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5
        }),
        
        // Plik z wszystkimi logami
        new winston.transports.File({
            filename: 'logs/combined.log',
            maxsize: 5242880, // 5MB
            maxFiles: 5
        })
    ]
});

// Dodaj stream dla morgan (jeśli używany)
logger.stream = {
    write: (message) => {
        logger.info(message.trim());
    }
};

// Funkcje pomocnicze
export const createChildLogger = (service) => {
    return logger.child({ service });
};

export const logPerformance = (operation, startTime) => {
    const duration = Date.now() - startTime;
    logger.info(`⏱️  ${operation} completed in ${duration}ms`);
};

export const logMemoryUsage = () => {
    const usage = process.memoryUsage();
    logger.info(`💾 Memory usage: ${Math.round(usage.heapUsed / 1024 / 1024)}MB`);
};

// Obsługa nieobsłużonych błędów
process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

export default logger;