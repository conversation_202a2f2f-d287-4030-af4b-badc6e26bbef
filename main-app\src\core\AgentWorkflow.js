import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Zaawansowany system workflow dla agentów AI
 */
export class AgentWorkflow extends EventEmitter {
    constructor() {
        super();
        this.workflows = new Map();
        this.activeExecutions = new Map();
        this.workflowTemplates = new Map();
        this.setupDefaultTemplates();
    }

    setupDefaultTemplates() {
        // Template dla rozwoju oprogramowania
        this.registerWorkflowTemplate('software_development', {
            name: 'Software Development Workflow',
            description: 'Kompleksowy workflow dla rozwoju oprogramowania',
            phases: [
                {
                    name: 'requirements_gathering',
                    agent: 'planning',
                    tasks: [
                        'analyze_requirements',
                        'identify_stakeholders',
                        'define_acceptance_criteria',
                        'estimate_complexity'
                    ],
                    outputs: ['requirements_document', 'stakeholder_map', 'acceptance_criteria']
                },
                {
                    name: 'research_and_analysis',
                    agent: 'research',
                    dependencies: ['requirements_gathering'],
                    tasks: [
                        'technology_research',
                        'competitive_analysis',
                        'best_practices_research',
                        'risk_identification'
                    ],
                    outputs: ['technology_recommendations', 'competitive_landscape', 'risk_matrix']
                },
                {
                    name: 'architecture_design',
                    agent: 'analysis',
                    dependencies: ['research_and_analysis'],
                    tasks: [
                        'system_architecture',
                        'database_design',
                        'api_specification',
                        'security_architecture'
                    ],
                    outputs: ['architecture_document', 'database_schema', 'api_specs']
                },
                {
                    name: 'implementation',
                    agent: 'coding',
                    dependencies: ['architecture_design'],
                    tasks: [
                        'code_generation',
                        'unit_testing',
                        'integration_testing',
                        'documentation'
                    ],
                    outputs: ['source_code', 'test_suite', 'documentation']
                },
                {
                    name: 'quality_assurance',
                    agent: 'analysis',
                    dependencies: ['implementation'],
                    tasks: [
                        'code_review',
                        'security_audit',
                        'performance_testing',
                        'compliance_check'
                    ],
                    outputs: ['quality_report', 'security_assessment', 'performance_metrics']
                }
            ]
        });

        // Template dla analizy biznesowej
        this.registerWorkflowTemplate('business_analysis', {
            name: 'Business Analysis Workflow',
            description: 'Workflow dla kompleksowej analizy biznesowej',
            phases: [
                {
                    name: 'problem_definition',
                    agent: 'planning',
                    tasks: [
                        'stakeholder_analysis',
                        'problem_statement',
                        'success_criteria',
                        'constraints_identification'
                    ]
                },
                {
                    name: 'market_research',
                    agent: 'research',
                    dependencies: ['problem_definition'],
                    tasks: [
                        'market_size_analysis',
                        'competitor_analysis',
                        'customer_research',
                        'trend_analysis'
                    ]
                },
                {
                    name: 'solution_analysis',
                    agent: 'analysis',
                    dependencies: ['market_research'],
                    tasks: [
                        'solution_options',
                        'cost_benefit_analysis',
                        'risk_assessment',
                        'implementation_roadmap'
                    ]
                }
            ]
        });

        // Template dla badań i rozwoju
        this.registerWorkflowTemplate('research_development', {
            name: 'Research & Development Workflow',
            description: 'Workflow dla projektów badawczo-rozwojowych',
            phases: [
                {
                    name: 'research_planning',
                    agent: 'planning',
                    tasks: [
                        'research_objectives',
                        'methodology_selection',
                        'resource_planning',
                        'timeline_definition'
                    ]
                },
                {
                    name: 'literature_review',
                    agent: 'research',
                    dependencies: ['research_planning'],
                    tasks: [
                        'academic_research',
                        'patent_analysis',
                        'technology_landscape',
                        'gap_analysis'
                    ]
                },
                {
                    name: 'experimental_design',
                    agent: 'analysis',
                    dependencies: ['literature_review'],
                    tasks: [
                        'hypothesis_formulation',
                        'experiment_design',
                        'metrics_definition',
                        'validation_criteria'
                    ]
                },
                {
                    name: 'prototype_development',
                    agent: 'coding',
                    dependencies: ['experimental_design'],
                    tasks: [
                        'prototype_implementation',
                        'testing_framework',
                        'data_collection',
                        'results_analysis'
                    ]
                }
            ]
        });
    }

    registerWorkflowTemplate(id, template) {
        this.workflowTemplates.set(id, {
            ...template,
            id,
            createdAt: new Date()
        });
        logger.info(`📋 Zarejestrowano template workflow: ${template.name}`);
    }

    async createWorkflow(templateId, customization = {}) {
        const template = this.workflowTemplates.get(templateId);
        if (!template) {
            throw new Error(`Template workflow '${templateId}' nie został znaleziony`);
        }

        const workflowId = uuidv4();
        const workflow = {
            id: workflowId,
            templateId,
            name: customization.name || template.name,
            description: customization.description || template.description,
            phases: this.customizePhases(template.phases, customization.phases || {}),
            status: 'created',
            currentPhase: null,
            results: new Map(),
            context: customization.context || {},
            createdAt: new Date(),
            startedAt: null,
            completedAt: null
        };

        this.workflows.set(workflowId, workflow);
        this.emit('workflow:created', workflowId, workflow);
        
        logger.info(`📋 Utworzono workflow: ${workflow.name} (${workflowId})`);
        return workflowId;
    }

    customizePhases(templatePhases, customizations) {
        return templatePhases.map(phase => ({
            ...phase,
            ...customizations[phase.name],
            id: uuidv4(),
            status: 'pending',
            startedAt: null,
            completedAt: null,
            results: null,
            errors: []
        }));
    }

    async executeWorkflow(workflowId, agentRegistry) {
        const workflow = this.workflows.get(workflowId);
        if (!workflow) {
            throw new Error(`Workflow '${workflowId}' nie został znaleziony`);
        }

        if (workflow.status !== 'created') {
            throw new Error(`Workflow '${workflowId}' ma nieprawidłowy status: ${workflow.status}`);
        }

        workflow.status = 'running';
        workflow.startedAt = new Date();
        this.activeExecutions.set(workflowId, workflow);

        this.emit('workflow:started', workflowId, workflow);
        logger.info(`🚀 Rozpoczęto wykonywanie workflow: ${workflow.name}`);

        try {
            const results = await this.executePhases(workflow, agentRegistry);
            
            workflow.status = 'completed';
            workflow.completedAt = new Date();
            workflow.results = results;

            this.emit('workflow:completed', workflowId, workflow, results);
            logger.info(`✅ Zakończono workflow: ${workflow.name}`);

            return results;
        } catch (error) {
            workflow.status = 'failed';
            workflow.error = error;

            this.emit('workflow:failed', workflowId, workflow, error);
            logger.error(`❌ Workflow nieudany: ${workflow.name}`, error);

            throw error;
        } finally {
            this.activeExecutions.delete(workflowId);
        }
    }

    async executePhases(workflow, agentRegistry) {
        const results = new Map();
        const completedPhases = new Set();

        for (const phase of workflow.phases) {
            // Sprawdź zależności
            if (phase.dependencies) {
                const unmetDependencies = phase.dependencies.filter(dep => 
                    !completedPhases.has(dep)
                );
                
                if (unmetDependencies.length > 0) {
                    throw new Error(`Niespełnione zależności dla fazy ${phase.name}: ${unmetDependencies.join(', ')}`);
                }
            }

            workflow.currentPhase = phase.name;
            phase.status = 'running';
            phase.startedAt = new Date();

            this.emit('phase:started', workflow.id, phase);
            logger.info(`🔄 Rozpoczęto fazę: ${phase.name} (agent: ${phase.agent})`);

            try {
                const agent = agentRegistry.getAgent(phase.agent);
                const phaseContext = this.buildPhaseContext(workflow, phase, results);
                
                const phaseResult = await this.executePhase(agent, phase, phaseContext);
                
                phase.status = 'completed';
                phase.completedAt = new Date();
                phase.results = phaseResult;
                
                results.set(phase.name, phaseResult);
                completedPhases.add(phase.name);

                this.emit('phase:completed', workflow.id, phase, phaseResult);
                logger.info(`✅ Zakończono fazę: ${phase.name}`);

            } catch (error) {
                phase.status = 'failed';
                phase.errors.push(error);

                this.emit('phase:failed', workflow.id, phase, error);
                logger.error(`❌ Faza nieudana: ${phase.name}`, error);

                throw error;
            }
        }

        return results;
    }

    buildPhaseContext(workflow, phase, previousResults) {
        const context = {
            workflowId: workflow.id,
            workflowName: workflow.name,
            phaseName: phase.name,
            globalContext: workflow.context,
            previousResults: Object.fromEntries(previousResults),
            requirements: phase.tasks || [],
            expectedOutputs: phase.outputs || []
        };

        // Dodaj wyniki z zależnych faz
        if (phase.dependencies) {
            context.dependencyResults = {};
            phase.dependencies.forEach(dep => {
                if (previousResults.has(dep)) {
                    context.dependencyResults[dep] = previousResults.get(dep);
                }
            });
        }

        return context;
    }

    async executePhase(agent, phase, context) {
        // Przygotuj zadanie dla agenta
        const task = {
            id: uuidv4(),
            type: 'workflow_phase',
            phase: phase.name,
            description: `Wykonaj fazę ${phase.name} w workflow`,
            requirements: phase.tasks || [],
            expectedOutputs: phase.outputs || [],
            context: context
        };

        // Wykonaj zadanie przez agenta
        const result = await agent.execute({ task, context });

        // Waliduj wyniki
        this.validatePhaseResults(result, phase);

        return result;
    }

    validatePhaseResults(result, phase) {
        if (!result) {
            throw new Error(`Agent nie zwrócił wyników dla fazy ${phase.name}`);
        }

        // Sprawdź czy wszystkie wymagane outputy zostały wygenerowane
        if (phase.outputs && phase.outputs.length > 0) {
            const missingOutputs = phase.outputs.filter(output => 
                !result.deliverables || !result.deliverables[output]
            );

            if (missingOutputs.length > 0) {
                logger.warn(`⚠️ Brakujące outputy w fazie ${phase.name}: ${missingOutputs.join(', ')}`);
            }
        }
    }

    getWorkflow(workflowId) {
        return this.workflows.get(workflowId);
    }

    getActiveWorkflows() {
        return Array.from(this.activeExecutions.values());
    }

    getWorkflowTemplates() {
        return Array.from(this.workflowTemplates.values());
    }

    getWorkflowStatus(workflowId) {
        const workflow = this.workflows.get(workflowId);
        if (!workflow) return null;

        return {
            id: workflow.id,
            name: workflow.name,
            status: workflow.status,
            currentPhase: workflow.currentPhase,
            progress: this.calculateProgress(workflow),
            phases: workflow.phases.map(phase => ({
                name: phase.name,
                status: phase.status,
                agent: phase.agent,
                startedAt: phase.startedAt,
                completedAt: phase.completedAt,
                duration: phase.completedAt && phase.startedAt ? 
                    phase.completedAt - phase.startedAt : null
            })),
            createdAt: workflow.createdAt,
            startedAt: workflow.startedAt,
            completedAt: workflow.completedAt,
            totalDuration: workflow.completedAt && workflow.startedAt ?
                workflow.completedAt - workflow.startedAt : null
        };
    }

    calculateProgress(workflow) {
        const totalPhases = workflow.phases.length;
        const completedPhases = workflow.phases.filter(p => p.status === 'completed').length;
        const runningPhases = workflow.phases.filter(p => p.status === 'running').length;

        return {
            total: totalPhases,
            completed: completedPhases,
            running: runningPhases,
            pending: totalPhases - completedPhases - runningPhases,
            percentage: Math.round((completedPhases / totalPhases) * 100)
        };
    }

    async pauseWorkflow(workflowId) {
        const workflow = this.workflows.get(workflowId);
        if (workflow && workflow.status === 'running') {
            workflow.status = 'paused';
            this.emit('workflow:paused', workflowId, workflow);
            logger.info(`⏸️ Wstrzymano workflow: ${workflow.name}`);
        }
    }

    async resumeWorkflow(workflowId, agentRegistry) {
        const workflow = this.workflows.get(workflowId);
        if (workflow && workflow.status === 'paused') {
            workflow.status = 'running';
            this.emit('workflow:resumed', workflowId, workflow);
            logger.info(`▶️ Wznowiono workflow: ${workflow.name}`);
            
            // Kontynuuj wykonywanie
            return await this.executeWorkflow(workflowId, agentRegistry);
        }
    }

    async cancelWorkflow(workflowId) {
        const workflow = this.workflows.get(workflowId);
        if (workflow && ['running', 'paused'].includes(workflow.status)) {
            workflow.status = 'cancelled';
            workflow.completedAt = new Date();
            this.activeExecutions.delete(workflowId);
            
            this.emit('workflow:cancelled', workflowId, workflow);
            logger.info(`🛑 Anulowano workflow: ${workflow.name}`);
        }
    }
}