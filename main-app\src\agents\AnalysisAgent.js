import { BaseAgent } from './BaseAgent.js';

/**
 * Analysis Agent - Agent odpowiedzialny za podsumowanie i przekazanie instrukcji
 */
export class AnalysisAgent extends BaseAgent {
    constructor(config) {
        super({
            ...config,
            capabilities: [
                'data_synthesis',
                'instruction_generation',
                'architecture_design',
                'implementation_planning',
                'quality_assessment',
                'risk_mitigation'
            ]
        });
    }

    async performTask(context) {
        const { task, planningResult, researchResult } = context;
        
        this.log('info', `Rozpoczynam analizę i syntezę dla zadania: ${task.description}`);

        // Synteza informacji z planowania i research
        const synthesis = await this.synthesizeInformation(planningResult, researchResult);
        
        // Projektowanie architektury
        const architecture = await this.designArchitecture(task, synthesis);
        
        // Generowanie szczegółowych instrukcji
        const detailedInstructions = await this.generateDetailedInstructions(task, synthesis, architecture);
        
        // Ocena jako<PERSON>ci i kompletności
        const qualityAssessment = await this.assessQuality(synthesis, architecture, detailedInstructions);
        
        // Plan implementacji
        const implementationPlan = await this.createImplementationPlan(detailedInstructions, qualityAssessment);
        
        // Mitigacja ryzyk
        const riskMitigation = await this.createRiskMitigation(planningResult.risks, researchResult.potentialIssues);

        const analysisResult = {
            taskId: task.id,
            synthesis,
            architecture,
            detailedInstructions,
            qualityAssessment,
            implementationPlan,
            riskMitigation,
            readyForImplementation: qualityAssessment.score >= 0.8,
            createdAt: new Date()
        };

        this.log('info', `Zakończono analizę. Gotowość do implementacji: ${analysisResult.readyForImplementation}`);
        
        return analysisResult;
    }

    async synthesizeInformation(planningResult, researchResult) {
        this.log('info', 'Synteza informacji z planowania i research...');
        
        const synthesis = {
            consolidatedRequirements: this.consolidateRequirements(planningResult, researchResult),
            optimizedSubtasks: this.optimizeSubtasks(planningResult.subtasks, researchResult),
            technologyStack: this.defineTechnologyStack(researchResult),
            bestPracticesIntegration: this.integrateBestPractices(researchResult.bestPractices),
            potentialChallenges: this.identifyChallenges(planningResult.risks, researchResult.potentialIssues)
        };

        return synthesis;
    }

    consolidateRequirements(planningResult, researchResult) {
        const consolidated = {
            functional: [...planningResult.requirements.functional],
            technical: [...planningResult.requirements.technical],
            quality: [],
            performance: []
        };

        // Dodaj wymagania jakościowe z research
        consolidated.quality.push('Code coverage minimum 80%');
        consolidated.quality.push('TypeScript strict mode compliance');
        consolidated.quality.push('Accessibility compliance (WCAG 2.1)');

        // Dodaj wymagania wydajnościowe
        consolidated.performance.push('Debounced search (300ms)');
        consolidated.performance.push('Optimized re-rendering');
        consolidated.performance.push('Lazy loading for large datasets');

        return consolidated;
    }

    optimizeSubtasks(originalSubtasks, researchResult) {
        return originalSubtasks.map(subtask => {
            const optimized = { ...subtask };

            // Dodaj szczegóły implementacyjne z research
            if (subtask.id === 'component-structure') {
                optimized.implementationDetails = {
                    approach: 'Functional component with TypeScript',
                    patterns: ['Custom hooks', 'Compound components'],
                    files: ['UserList.tsx', 'UserList.types.ts', 'UserList.module.css']
                };
            }

            if (subtask.id === 'pagination') {
                optimized.implementationDetails = {
                    approach: 'Custom usePagination hook',
                    features: ['Page navigation', 'Items per page control', 'Total count display'],
                    files: ['usePagination.ts', 'Pagination.tsx']
                };
            }

            if (subtask.id === 'search') {
                optimized.implementationDetails = {
                    approach: 'Debounced search with useSearch hook',
                    features: ['Real-time filtering', 'Multiple field search', 'Search highlighting'],
                    files: ['useSearch.ts', 'SearchInput.tsx']
                };
            }

            return optimized;
        });
    }

    defineTechnologyStack(researchResult) {
        return {
            core: {
                react: researchResult.frameworkAnalysis.react.version,
                typescript: researchResult.frameworkAnalysis.typescript.version,
                nodeVersion: '18+'
            },
            testing: {
                framework: 'Jest',
                library: 'React Testing Library',
                coverage: 'c8 or built-in Jest coverage'
            },
            styling: {
                approach: 'CSS Modules',
                preprocessor: 'SCSS (optional)',
                utilities: 'clsx for conditional classes'
            },
            development: {
                bundler: 'Vite or Create React App',
                linting: 'ESLint + Prettier',
                typeChecking: 'TypeScript compiler'
            }
        };
    }

    async designArchitecture(task, synthesis) {
        this.log('info', 'Projektowanie architektury komponentu...');
        
        return {
            componentStructure: {
                main: 'UserList',
                subcomponents: ['UserItem', 'Pagination', 'SearchInput', 'LoadingSpinner'],
                hooks: ['usePagination', 'useSearch', 'useUserList'],
                types: ['User', 'UserListProps', 'PaginationProps', 'SearchProps']
            },
            fileStructure: {
                'components/': {
                    'UserList/': {
                        'UserList.tsx': 'Main component',
                        'UserList.types.ts': 'TypeScript interfaces',
                        'UserList.module.css': 'Component styles',
                        'UserList.test.tsx': 'Component tests',
                        'index.ts': 'Barrel export'
                    },
                    'UserItem/': {
                        'UserItem.tsx': 'Individual user component',
                        'UserItem.types.ts': 'User item types',
                        'UserItem.module.css': 'User item styles'
                    }
                },
                'hooks/': {
                    'usePagination.ts': 'Pagination logic',
                    'useSearch.ts': 'Search logic',
                    'useUserList.ts': 'Main data management'
                },
                'types/': {
                    'User.ts': 'User entity types',
                    'common.ts': 'Common types'
                }
            },
            dataFlow: {
                'UserList': 'Main state management',
                'useUserList': 'Data fetching and caching',
                'usePagination': 'Pagination state',
                'useSearch': 'Search filtering',
                'UserItem': 'Individual item rendering'
            }
        };
    }

    async generateDetailedInstructions(task, synthesis, architecture) {
        this.log('info', 'Generowanie szczegółowych instrukcji implementacji...');
        
        return {
            setup: {
                step: 1,
                title: 'Konfiguracja środowiska',
                instructions: [
                    'Stwórz folder components/UserList',
                    'Skonfiguruj TypeScript (tsconfig.json)',
                    'Zainstaluj zależności: @types/react, @types/react-dom',
                    'Skonfiguruj CSS Modules w bundlerze'
                ],
                files: ['tsconfig.json', 'package.json'],
                estimatedTime: '15min'
            },
            types: {
                step: 2,
                title: 'Definicja typów TypeScript',
                instructions: [
                    'Stwórz interfejs User w types/User.ts',
                    'Zdefiniuj UserListProps w UserList.types.ts',
                    'Dodaj typy dla pagination i search',
                    'Eksportuj wszystkie typy przez index.ts'
                ],
                files: ['types/User.ts', 'components/UserList/UserList.types.ts'],
                code: {
                    'types/User.ts': `
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  createdAt: Date;
}`,
                    'UserList.types.ts': `
import { User } from '../../types/User';

export interface UserListProps {
  users: User[];
  loading?: boolean;
  onUserSelect?: (user: User) => void;
  searchable?: boolean;
  paginated?: boolean;
  itemsPerPage?: number;
}`
                },
                estimatedTime: '20min'
            },
            hooks: {
                step: 3,
                title: 'Implementacja custom hooks',
                instructions: [
                    'Stwórz usePagination hook',
                    'Implementuj useSearch hook z debounce',
                    'Stwórz useUserList hook dla głównej logiki',
                    'Dodaj testy dla każdego hook'
                ],
                files: ['hooks/usePagination.ts', 'hooks/useSearch.ts', 'hooks/useUserList.ts'],
                estimatedTime: '45min'
            },
            components: {
                step: 4,
                title: 'Implementacja komponentów',
                instructions: [
                    'Stwórz główny komponent UserList',
                    'Implementuj UserItem subkomponent',
                    'Dodaj SearchInput komponent',
                    'Stwórz Pagination komponent',
                    'Zintegruj wszystkie części'
                ],
                files: ['UserList.tsx', 'UserItem.tsx', 'SearchInput.tsx', 'Pagination.tsx'],
                estimatedTime: '60min'
            },
            styling: {
                step: 5,
                title: 'Stylowanie komponentów',
                instructions: [
                    'Stwórz CSS Modules dla każdego komponentu',
                    'Implementuj responsive design',
                    'Dodaj hover states i transitions',
                    'Zapewnij accessibility (focus states)'
                ],
                files: ['UserList.module.css', 'UserItem.module.css'],
                estimatedTime: '30min'
            },
            testing: {
                step: 6,
                title: 'Implementacja testów',
                instructions: [
                    'Napisz testy dla każdego hook',
                    'Stwórz testy komponentów z React Testing Library',
                    'Dodaj integration testy',
                    'Sprawdź coverage (minimum 80%)'
                ],
                files: ['UserList.test.tsx', 'hooks/__tests__/'],
                estimatedTime: '45min'
            }
        };
    }

    async assessQuality(synthesis, architecture, detailedInstructions) {
        this.log('info', 'Ocena jakości i kompletności instrukcji...');
        
        const criteria = {
            completeness: this.assessCompleteness(detailedInstructions),
            clarity: this.assessClarity(detailedInstructions),
            feasibility: this.assessFeasibility(architecture, detailedInstructions),
            bestPractices: this.assessBestPracticesCompliance(synthesis, architecture),
            testability: this.assessTestability(architecture, detailedInstructions)
        };

        const score = Object.values(criteria).reduce((sum, score) => sum + score, 0) / Object.keys(criteria).length;

        return {
            score,
            criteria,
            recommendations: this.generateQualityRecommendations(criteria),
            readyForImplementation: score >= 0.8
        };
    }

    assessCompleteness(instructions) {
        const requiredSteps = ['setup', 'types', 'hooks', 'components', 'styling', 'testing'];
        const providedSteps = Object.keys(instructions);
        return requiredSteps.every(step => providedSteps.includes(step)) ? 1.0 : 0.7;
    }

    assessClarity(instructions) {
        // Sprawdź czy każdy krok ma jasne instrukcje i przykłady kodu
        const stepsWithCode = Object.values(instructions).filter(step => step.code || step.instructions.length > 2);
        return stepsWithCode.length / Object.keys(instructions).length;
    }

    assessFeasibility(architecture, instructions) {
        // Sprawdź czy architektura jest realistyczna do implementacji
        const estimatedTotalTime = Object.values(instructions).reduce((total, step) => {
            const time = parseInt(step.estimatedTime) || 0;
            return total + time;
        }, 0);
        
        return estimatedTotalTime <= 240 ? 1.0 : 0.8; // Max 4 godziny
    }

    assessBestPracticesCompliance(synthesis, architecture) {
        const practices = [
            architecture.componentStructure.hooks.length > 0, // Custom hooks
            architecture.fileStructure['types/'], // Proper typing
            synthesis.technologyStack.testing, // Testing setup
            architecture.componentStructure.subcomponents.length > 1 // Component composition
        ];
        
        return practices.filter(Boolean).length / practices.length;
    }

    assessTestability(architecture, instructions) {
        return instructions.testing ? 1.0 : 0.5;
    }

    async createImplementationPlan(detailedInstructions, qualityAssessment) {
        const steps = Object.values(detailedInstructions).sort((a, b) => a.step - b.step);
        
        return {
            totalSteps: steps.length,
            estimatedTotalTime: steps.reduce((total, step) => {
                const time = parseInt(step.estimatedTime) || 0;
                return total + time;
            }, 0),
            criticalPath: ['setup', 'types', 'hooks', 'components'],
            parallelizable: ['styling', 'testing'],
            dependencies: {
                'types': [],
                'hooks': ['types'],
                'components': ['types', 'hooks'],
                'styling': ['components'],
                'testing': ['components']
            },
            qualityGates: [
                { after: 'types', check: 'TypeScript compilation' },
                { after: 'hooks', check: 'Hook tests passing' },
                { after: 'components', check: 'Component rendering' },
                { after: 'testing', check: 'Coverage >= 80%' }
            ]
        };
    }

    async createRiskMitigation(planningRisks, researchIssues) {
        const allRisks = [...planningRisks, ...researchIssues];
        
        return allRisks.map(risk => ({
            risk: risk.description || risk.issue,
            category: risk.type || risk.category,
            mitigation: risk.mitigation || risk.solution,
            priority: this.calculateRiskPriority(risk),
            preventiveActions: this.generatePreventiveActions(risk)
        }));
    }

    calculateRiskPriority(risk) {
        // Prosta logika priorytetyzacji ryzyk
        if (risk.impact === 'high' || risk.category === 'Performance') return 'high';
        if (risk.impact === 'medium' || risk.category === 'TypeScript') return 'medium';
        return 'low';
    }

    generatePreventiveActions(risk) {
        const actions = [];
        
        if (risk.category === 'Performance') {
            actions.push('Implementuj React.memo dla komponentów');
            actions.push('Użyj useMemo dla expensive calculations');
        }
        
        if (risk.category === 'TypeScript') {
            actions.push('Skonfiguruj strict mode');
            actions.push('Dodaj pre-commit hooks dla type checking');
        }
        
        return actions;
    }

    integrateBestPractices(bestPractices) {
        return {
            react: bestPractices.react.slice(0, 3), // Top 3 practices
            typescript: bestPractices.typescript.slice(0, 3),
            testing: bestPractices.testing.slice(0, 3),
            performance: bestPractices.performance.slice(0, 2)
        };
    }

    identifyChallenges(risks, issues) {
        return [...risks, ...issues].map(item => ({
            challenge: item.description || item.issue,
            difficulty: 'medium',
            timeImpact: '10-20%',
            solution: item.mitigation || item.solution
        }));
    }

    generateQualityRecommendations(criteria) {
        const recommendations = [];
        
        if (criteria.completeness < 0.9) {
            recommendations.push('Dodaj brakujące kroki implementacji');
        }
        
        if (criteria.clarity < 0.8) {
            recommendations.push('Dodaj więcej przykładów kodu i szczegółów');
        }
        
        if (criteria.testability < 0.9) {
            recommendations.push('Rozszerz sekcję testowania');
        }
        
        return recommendations;
    }
}