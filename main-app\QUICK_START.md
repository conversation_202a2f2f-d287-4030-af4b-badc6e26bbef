# 🚀 Quick Start Guide

## Szybkie uruchomienie systemu Multi-Agent AI

### 1. Instalacja (2 minuty)

```bash
# Przejdź do katalogu aplikacji
cd main-app

# Zainstaluj zależności
npm install
```

### 2. Konfigu<PERSON><PERSON> (1 minuta)

```bash
# Skopiuj przykładową konfigurację
cp .env.example .env
```

**Gotowe!** Serwer @21st-dev/magic ma już skonfigurowany API key.

### 3. Uruchomienie (30 sekund)

```bash
# Uruchom system
npm start
```

### 4. <PERSON>y (opcjonalne)

```bash
# Test całego systemu
npm run test:system

# Test tylko @21st-dev/magic
npm run test:magic

# Test lokalnych narzędzi
npm run test:mcp
```

## 🎯 Co się dzieje po uruchomieniu?

1. **Inicjalizacja komponentów**
   - MCPManager łączy się z serwerami
   - TaskManager uruchamia kolejkę zadań
   - Agenci rejestrują swoje możliwości

2. **Tworzenie przykładowych zadań**
   - Komponent React (priorytet: wysoki)
   - Dokumentacja API (zależy od task-001)
   - Testy integracyjne (zależy od task-001)

3. **Wykonywanie zadań**
   - SuperAgent orkiestruje przepływ pracy
   - Zadania wykonywane według priorytetów
   - Zależności respektowane automatycznie

## 🔧 Dostępne serwery MCP

Po uruchomieniu system próbuje połączyć się z:

- **@21st-dev/magic** ✅ (gotowy API key)
- **Context7** (wymaga API key)
- **21st.dev** (wymaga API key)  
- **Local Tools** ✅ (zawsze dostępny)

## 📊 Monitoring

```bash
# Sprawdź logi w czasie rzeczywistym
tail -f logs/system.log

# Status w aplikacji
# System automatycznie wyświetla status wszystkich komponentów
```

## 🛠️ Rozwiązywanie problemów

### Błąd połączenia MCP
```bash
# Sprawdź czy masz Node.js
node --version

# Sprawdź połączenie internetowe
ping google.com
```

### Brak logów
```bash
# Utwórz katalog logów
mkdir -p logs
```

### Błędy uprawnień (Windows)
```bash
# Uruchom jako administrator lub użyj PowerShell
```

## 🎉 Gotowe!

System jest teraz uruchomiony i gotowy do pracy. Sprawdź logi aby zobaczyć postęp wykonywania zadań.

**Następne kroki:**
- Dodaj własne zadania przez API
- Skonfiguruj dodatkowe serwery MCP
- Rozszerz funkcjonalność agentów