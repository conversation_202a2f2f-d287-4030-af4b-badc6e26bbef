{"mcpServers": {"@upstash/context7-mcp": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"UPSTASH_REDIS_REST_URL": "", "UPSTASH_REDIS_REST_TOKEN": ""}}, "@21st-dev/mcp-server": {"command": "npx", "args": ["@21st-dev/mcp-server"], "env": {"TWENTYFIRST_API_KEY": "", "TWENTYFIRST_ENDPOINT": "https://api.21st.dev"}}, "@21st-dev/magic": {"command": "cmd", "args": ["/c", "npx", "-y", "@21st-dev/magic@latest", "API_KEY=\"a2963c042498ab5d207844614351b4db01af277ca1689ef267381d99dbd8eb93\""]}, "local-tools": {"command": "node", "args": ["./src/mcp/servers/local-tools-server.js"]}, "@modelcontextprotocol/server-github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": ""}}, "@modelcontextprotocol/server-brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_SEARCH_API_KEY": ""}}, "@modelcontextprotocol/server-puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "@modelcontextprotocol/server-everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"]}}}