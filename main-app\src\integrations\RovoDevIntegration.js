import { logger } from '../utils/logger.js';
import axios from 'axios';

/**
 * Integracja z RovoDev - zaplecze AI dla wsparcia kodowania
 */
export class RovoDevIntegration {
    constructor() {
        this.isInitialized = false;
        this.apiKey = process.env.ROVO_DEV_API_KEY;
        this.endpoint = process.env.ROVO_DEV_ENDPOINT || 'https://api.rovo.dev';
        this.client = null;
        this.capabilities = [
            'code_analysis',
            'code_generation', 
            'code_review',
            'debugging_assistance',
            'architecture_suggestions',
            'performance_optimization',
            'security_analysis',
            'documentation_generation'
        ];
    }

    async initialize() {
        logger.info('🤖 Inicjalizacja integracji RovoDev...');
        
        try {
            // Konfiguracja klienta HTTP
            this.client = axios.create({
                baseURL: this.endpoint,
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'Multi-Agent-AI-System/1.0.0'
                },
                timeout: 30000
            });

            // Test połączenia
            if (this.apiKey) {
                await this.testConnection();
            } else {
                logger.warn('⚠️ Brak API key dla RovoDev - funkcje ograniczone');
            }
            
            this.isInitialized = true;
            logger.info('✅ Integracja RovoDev zainicjalizowana');
        } catch (error) {
            logger.error('❌ Błąd inicjalizacji integracji RovoDev:', error.message);
            // Nie rzucaj błędu - pozwól systemowi działać bez RovoDev
        }
    }

    async testConnection() {
        try {
            const response = await this.client.get('/health');
            logger.info('✅ Połączenie z RovoDev API potwierdzone');
            return response.data;
        } catch (error) {
            logger.warn('⚠️ Nie udało się połączyć z RovoDev API:', error.message);
            throw error;
        }
    }

    /**
     * Analizuje kod pod kątem jakości i potencjalnych problemów
     */
    async analyzeCode(code, language = 'javascript', options = {}) {
        if (!this.isInitialized || !this.apiKey) {
            return this.fallbackCodeAnalysis(code, language);
        }

        try {
            const response = await this.client.post('/analyze/code', {
                code,
                language,
                options: {
                    checkSecurity: true,
                    checkPerformance: true,
                    checkBestPractices: true,
                    ...options
                }
            });

            return {
                success: true,
                analysis: response.data,
                suggestions: response.data.suggestions || [],
                issues: response.data.issues || [],
                score: response.data.score || 0
            };
        } catch (error) {
            logger.error('❌ Błąd analizy kodu RovoDev:', error.message);
            return this.fallbackCodeAnalysis(code, language);
        }
    }

    /**
     * Generuje kod na podstawie opisu
     */
    async generateCode(description, language = 'javascript', context = {}) {
        if (!this.isInitialized || !this.apiKey) {
            return this.fallbackCodeGeneration(description, language);
        }

        try {
            const response = await this.client.post('/generate/code', {
                description,
                language,
                context: {
                    framework: context.framework || 'vanilla',
                    style: context.style || 'modern',
                    includeTests: context.includeTests || false,
                    includeComments: context.includeComments || true,
                    ...context
                }
            });

            return {
                success: true,
                code: response.data.code,
                explanation: response.data.explanation,
                tests: response.data.tests,
                dependencies: response.data.dependencies || []
            };
        } catch (error) {
            logger.error('❌ Błąd generowania kodu RovoDev:', error.message);
            return this.fallbackCodeGeneration(description, language);
        }
    }

    /**
     * Przeprowadza code review
     */
    async reviewCode(code, language = 'javascript', reviewType = 'comprehensive') {
        if (!this.isInitialized || !this.apiKey) {
            return this.fallbackCodeReview(code, language);
        }

        try {
            const response = await this.client.post('/review/code', {
                code,
                language,
                reviewType, // 'quick', 'comprehensive', 'security', 'performance'
                includeRefactoringSuggestions: true
            });

            return {
                success: true,
                review: response.data,
                rating: response.data.rating || 'unknown',
                issues: response.data.issues || [],
                suggestions: response.data.suggestions || [],
                refactoring: response.data.refactoring || null
            };
        } catch (error) {
            logger.error('❌ Błąd code review RovoDev:', error.message);
            return this.fallbackCodeReview(code, language);
        }
    }

    /**
     * Pomaga w debugowaniu kodu
     */
    async debugCode(code, error, language = 'javascript') {
        if (!this.isInitialized || !this.apiKey) {
            return this.fallbackDebugging(code, error);
        }

        try {
            const response = await this.client.post('/debug/code', {
                code,
                error: {
                    message: error.message || error,
                    stack: error.stack || '',
                    type: error.name || 'Error'
                },
                language
            });

            return {
                success: true,
                diagnosis: response.data.diagnosis,
                solutions: response.data.solutions || [],
                fixedCode: response.data.fixedCode,
                explanation: response.data.explanation
            };
        } catch (error) {
            logger.error('❌ Błąd debugowania RovoDev:', error.message);
            return this.fallbackDebugging(code, error);
        }
    }

    /**
     * Generuje dokumentację dla kodu
     */
    async generateDocumentation(code, language = 'javascript', format = 'markdown') {
        if (!this.isInitialized || !this.apiKey) {
            return this.fallbackDocumentation(code, language);
        }

        try {
            const response = await this.client.post('/generate/documentation', {
                code,
                language,
                format, // 'markdown', 'jsdoc', 'sphinx', 'javadoc'
                includeExamples: true,
                includeApiReference: true
            });

            return {
                success: true,
                documentation: response.data.documentation,
                examples: response.data.examples || [],
                apiReference: response.data.apiReference || null
            };
        } catch (error) {
            logger.error('❌ Błąd generowania dokumentacji RovoDev:', error.message);
            return this.fallbackDocumentation(code, language);
        }
    }

    // Fallback methods - podstawowe funkcje bez API
    fallbackCodeAnalysis(code, language) {
        return {
            success: false,
            analysis: {
                message: 'Analiza kodu niedostępna - brak połączenia z RovoDev API',
                basicChecks: {
                    length: code.length,
                    lines: code.split('\n').length,
                    language: language
                }
            },
            suggestions: ['Skonfiguruj RovoDev API dla pełnej analizy kodu'],
            issues: [],
            score: 0
        };
    }

    fallbackCodeGeneration(description, language) {
        return {
            success: false,
            code: `// Generowanie kodu niedostępne\n// Opis: ${description}\n// Język: ${language}\n// Skonfiguruj RovoDev API dla pełnej funkcjonalności`,
            explanation: 'Generowanie kodu wymaga połączenia z RovoDev API',
            tests: null,
            dependencies: []
        };
    }

    fallbackCodeReview(code, language) {
        return {
            success: false,
            review: {
                message: 'Code review niedostępny - brak połączenia z RovoDev API',
                basicInfo: {
                    length: code.length,
                    lines: code.split('\n').length,
                    language: language
                }
            },
            rating: 'unknown',
            issues: [],
            suggestions: ['Skonfiguruj RovoDev API dla pełnego code review'],
            refactoring: null
        };
    }

    fallbackDebugging(code, error) {
        return {
            success: false,
            diagnosis: 'Debugowanie niedostępne - brak połączenia z RovoDev API',
            solutions: ['Skonfiguruj RovoDev API dla pomocy w debugowaniu'],
            fixedCode: null,
            explanation: `Błąd: ${error.message || error}`
        };
    }

    fallbackDocumentation(code, language) {
        return {
            success: false,
            documentation: `# Dokumentacja\n\nGenerowanie dokumentacji niedostępne.\nSkonfiguruj RovoDev API dla pełnej funkcjonalności.\n\n**Język:** ${language}\n**Rozmiar kodu:** ${code.length} znaków`,
            examples: [],
            apiReference: null
        };
    }

    /**
     * Zwraca status integracji
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            connected: this.isInitialized && !!this.apiKey,
            endpoint: this.endpoint,
            capabilities: this.capabilities,
            hasApiKey: !!this.apiKey
        };
    }

    /**
     * Zwraca dostępne możliwości
     */
    getCapabilities() {
        return [...this.capabilities];
    }
}