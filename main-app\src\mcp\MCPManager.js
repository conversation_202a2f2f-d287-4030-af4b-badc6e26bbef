import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { logger } from '../utils/logger.js';

/**
 * Manager zarządzający połączeniami z serwerami MCP
 */
export class MCPManager {
    constructor() {
        this.servers = new Map();
        this.clients = new Map();
        this.isInitialized = false;
    }

    async initialize() {
        logger.info('🔌 Inicjalizacja MCP Manager...');
        
        try {
            // Konfiguracja serwerów MCP
            await this.setupMCPServers();
            
            // Połączenie z serwerami
            await this.connectToServers();
            
            this.isInitialized = true;
            logger.info('✅ MCP Manager zainicjalizowany pomyślnie');
        } catch (error) {
            logger.error('❌ Błąd inicjalizacji MCP Manager:', error);
            throw error;
        }
    }

    async setupMCPServers() {
        // Załaduj konfigurację z pliku jeśli istnieje
        await this.loadConfigFromFile();
        
        // Konfiguracja Context7 MCP Server (Upstash)
        this.servers.set('context7', {
            name: 'Context7 (Upstash)',
            command: 'npx',
            args: ['-y', '@upstash/context7-mcp'],
            description: 'Serwer MCP do zarządzania kontekstem (Upstash)',
            capabilities: ['context_management', 'memory', 'retrieval', 'vector_search'],
            env: {
                UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL || '',
                UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN || ''
            }
        });

        // Konfiguracja 21st.dev MCP Server
        this.servers.set('21st-dev', {
            name: '21st.dev',
            command: 'npx',
            args: ['@21st-dev/mcp-server'],
            description: 'Serwer MCP dla zaawansowanych funkcji AI',
            capabilities: ['ai_tools', 'code_analysis', 'optimization'],
            env: {
                TWENTYFIRST_API_KEY: process.env.TWENTYFIRST_API_KEY || '',
                TWENTYFIRST_ENDPOINT: process.env.TWENTYFIRST_ENDPOINT || 'https://api.21st.dev'
            }
        });

        // Konfiguracja 21st.dev Magic MCP Server
        this.servers.set('21st-dev-magic', {
            name: '21st.dev Magic',
            command: 'cmd',
            args: [
                '/c',
                'npx',
                '-y',
                '@21st-dev/magic@latest',
                `API_KEY="${process.env.TWENTYFIRST_MAGIC_API_KEY || 'a2963c042498ab5d207844614351b4db01af277ca1689ef267381d99dbd8eb93'}"`
            ],
            description: 'Serwer MCP 21st.dev Magic dla zaawansowanych funkcji AI',
            capabilities: ['magic_tools', 'ai_assistance', 'code_generation', 'analysis'],
            env: {
                TWENTYFIRST_MAGIC_API_KEY: process.env.TWENTYFIRST_MAGIC_API_KEY || 'a2963c042498ab5d207844614351b4db01af277ca1689ef267381d99dbd8eb93'
            }
        });

        // Konfiguracja GitHub MCP Server
        this.servers.set('github', {
            name: 'GitHub',
            command: 'npx',
            args: ['-y', '@modelcontextprotocol/server-github'],
            description: 'Serwer MCP do integracji z GitHub',
            capabilities: ['github_api', 'repositories', 'issues', 'pull_requests'],
            env: {
                GITHUB_PERSONAL_ACCESS_TOKEN: process.env.GITHUB_PERSONAL_ACCESS_TOKEN || ''
            }
        });

        // Konfiguracja Browser Search MCP Server
        this.servers.set('browser-search', {
            name: 'Browser Search',
            command: 'npx',
            args: ['-y', '@modelcontextprotocol/server-brave-search'],
            description: 'Serwer MCP do wyszukiwania w internecie',
            capabilities: ['web_search', 'brave_search', 'internet_access'],
            env: {
                BRAVE_SEARCH_API_KEY: process.env.BRAVE_SEARCH_API_KEY || ''
            }
        });

        // Konfiguracja Puppeteer MCP Server (przeglądarka)
        this.servers.set('puppeteer', {
            name: 'Puppeteer Browser',
            command: 'npx',
            args: ['-y', '@modelcontextprotocol/server-puppeteer'],
            description: 'Serwer MCP do automatyzacji przeglądarki',
            capabilities: ['browser_automation', 'web_scraping', 'screenshots'],
            env: {}
        });

        // Konfiguracja Desktop MCP Server (pulpit użytkownika)
        this.servers.set('desktop', {
            name: 'Desktop Integration',
            command: 'npx',
            args: ['-y', '@modelcontextprotocol/server-everything'],
            description: 'Serwer MCP do integracji z pulpitem użytkownika',
            capabilities: ['desktop_access', 'file_search', 'system_integration'],
            env: {}
        });

        // Konfiguracja lokalnego serwera narzędzi
        this.servers.set('local-tools', {
            name: 'Local Tools',
            command: 'node',
            args: ['./src/mcp/servers/local-tools-server.js'],
            description: 'Lokalny serwer narzędzi MCP',
            capabilities: ['file_operations', 'system_info', 'utilities']
        });

        logger.info(`📋 Skonfigurowano ${this.servers.size} serwerów MCP`);
    }

    async loadConfigFromFile() {
        try {
            const fs = await import('fs/promises');
            const configPath = './mcp-config.json';
            const configData = await fs.readFile(configPath, 'utf8');
            const config = JSON.parse(configData);
            
            if (config.mcpServers) {
                for (const [serverId, serverConfig] of Object.entries(config.mcpServers)) {
                    // Konwertuj konfigurację z pliku do formatu wewnętrznego
                    const internalConfig = {
                        name: serverId,
                        command: serverConfig.command,
                        args: serverConfig.args,
                        description: `Serwer MCP: ${serverId}`,
                        capabilities: serverConfig.capabilities || [],
                        env: { ...process.env, ...serverConfig.env }
                    };
                    
                    this.servers.set(serverId.replace('@', '').replace('/', '-'), internalConfig);
                }
                
                logger.info(`📂 Załadowano konfigurację serwerów MCP z pliku`);
            }
        } catch (error) {
            logger.info(`📋 Używam domyślnej konfiguracji serwerów MCP (brak pliku konfiguracyjnego)`);
        }
    }

    async connectToServers() {
        for (const [serverId, serverConfig] of this.servers) {
            try {
                await this.connectToServer(serverId, serverConfig);
            } catch (error) {
                logger.warn(`⚠️ Nie udało się połączyć z serwerem ${serverId}:`, error.message);
                // Kontynuuj z innymi serwerami
            }
        }
    }

    async connectToServer(serverId, serverConfig) {
        logger.info(`🔌 Łączenie z serwerem MCP: ${serverConfig.name}`);

        const transport = new StdioClientTransport({
            command: serverConfig.command,
            args: serverConfig.args,
            env: { ...process.env, ...serverConfig.env }
        });

        const client = new Client({
            name: `multi-agent-system-${serverId}`,
            version: '1.0.0'
        }, {
            capabilities: {
                tools: {},
                resources: {},
                prompts: {}
            }
        });

        await client.connect(transport);
        
        // Pobierz dostępne narzędzia
        const tools = await client.listTools();
        const resources = await client.listResources();
        const prompts = await client.listPrompts();

        this.clients.set(serverId, {
            client,
            transport,
            config: serverConfig,
            tools: tools.tools || [],
            resources: resources.resources || [],
            prompts: prompts.prompts || [],
            connectedAt: new Date(),
            status: 'connected'
        });

        logger.info(`✅ Połączono z ${serverConfig.name} - narzędzia: ${tools.tools?.length || 0}, zasoby: ${resources.resources?.length || 0}`);
    }

    /**
     * Wykonuje narzędzie MCP na określonym serwerze
     */
    async callTool(serverId, toolName, arguments_) {
        const serverClient = this.clients.get(serverId);
        if (!serverClient) {
            throw new Error(`Serwer MCP '${serverId}' nie jest dostępny`);
        }

        if (serverClient.status !== 'connected') {
            throw new Error(`Serwer MCP '${serverId}' nie jest połączony`);
        }

        try {
            logger.info(`🔧 Wywołanie narzędzia ${toolName} na serwerze ${serverId}`);
            
            const result = await serverClient.client.callTool({
                name: toolName,
                arguments: arguments_
            });

            return result;
        } catch (error) {
            logger.error(`❌ Błąd wywołania narzędzia ${toolName} na ${serverId}:`, error);
            throw error;
        }
    }

    /**
     * Pobiera zasób z serwera MCP
     */
    async getResource(serverId, resourceUri) {
        const serverClient = this.clients.get(serverId);
        if (!serverClient) {
            throw new Error(`Serwer MCP '${serverId}' nie jest dostępny`);
        }

        try {
            const result = await serverClient.client.readResource({
                uri: resourceUri
            });

            return result;
        } catch (error) {
            logger.error(`❌ Błąd pobierania zasobu ${resourceUri} z ${serverId}:`, error);
            throw error;
        }
    }

    /**
     * Zwraca listę dostępnych narzędzi ze wszystkich serwerów
     */
    getAllAvailableTools() {
        const allTools = [];
        
        for (const [serverId, serverClient] of this.clients) {
            if (serverClient.status === 'connected') {
                serverClient.tools.forEach(tool => {
                    allTools.push({
                        serverId,
                        serverName: serverClient.config.name,
                        ...tool
                    });
                });
            }
        }

        return allTools;
    }

    /**
     * Zwraca status wszystkich serwerów MCP
     */
    getStatus() {
        const status = {
            initialized: this.isInitialized,
            servers: {},
            totalServers: this.servers.size,
            connectedServers: 0,
            totalTools: 0,
            totalResources: 0
        };

        for (const [serverId, serverClient] of this.clients) {
            status.servers[serverId] = {
                name: serverClient.config.name,
                status: serverClient.status,
                connectedAt: serverClient.connectedAt,
                toolsCount: serverClient.tools.length,
                resourcesCount: serverClient.resources.length,
                capabilities: serverClient.config.capabilities
            };

            if (serverClient.status === 'connected') {
                status.connectedServers++;
                status.totalTools += serverClient.tools.length;
                status.totalResources += serverClient.resources.length;
            }
        }

        return status;
    }

    /**
     * Zamyka wszystkie połączenia MCP
     */
    async shutdown() {
        logger.info('🛑 Zamykanie połączeń MCP...');

        for (const [serverId, serverClient] of this.clients) {
            try {
                await serverClient.client.close();
                logger.info(`✅ Zamknięto połączenie z ${serverId}`);
            } catch (error) {
                logger.error(`❌ Błąd zamykania połączenia z ${serverId}:`, error);
            }
        }

        this.clients.clear();
        this.isInitialized = false;
    }
}