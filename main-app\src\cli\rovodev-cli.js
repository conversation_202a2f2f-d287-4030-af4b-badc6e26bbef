#!/usr/bin/env node

import { Command } from 'commander';
import { SuperAgent } from '../core/SuperAgent.js';
import { AgentRegistry } from '../core/AgentRegistry.js';
import { TaskManager } from '../core/TaskManager.js';
import { MCPManager } from '../mcp/MCPManager.js';
import { AtlassianIntegration } from '../integrations/AtlassianIntegration.js';
import { RovoDevIntegration } from '../integrations/RovoDevIntegration.js';
import { logger } from '../utils/logger.js';
import dotenv from 'dotenv';
import fs from 'fs/promises';
import path from 'path';

// Załaduj konfigurację
dotenv.config();

const program = new Command();

// Globalne komponenty
let superAgent;
let taskManager;
let mcpManager;

async function initializeSystem() {
    if (superAgent) return superAgent;

    try {
        logger.info('🚀 Inicjalizacja RovoDev CLI...');

        // Inicjalizacja komponentów
        mcpManager = new MCPManager();
        const atlassianIntegration = new AtlassianIntegration();
        const rovoDevIntegration = new RovoDevIntegration();
        const agentRegistry = new AgentRegistry();
        taskManager = new TaskManager();
        
        // Inicjalizacja SuperAgent
        superAgent = new SuperAgent({
            mcpManager,
            atlassianIntegration,
            rovoDevIntegration,
            agentRegistry,
            taskManager
        });

        // Rejestracja agentów
        await agentRegistry.registerAgents();
        
        // Inicjalizacja komponentów
        await taskManager.initialize();
        await rovoDevIntegration.initialize();
        await superAgent.initialize();
        
        logger.info('✅ System RovoDev CLI zainicjalizowany!');
        return superAgent;
    } catch (error) {
        logger.error('❌ Błąd inicjalizacji:', error);
        process.exit(1);
    }
}

// Konfiguracja CLI
program
    .name('rovodev')
    .description('RovoDev CLI - Zarządzanie zadaniami i agentami AI')
    .version('1.0.0');

// Komenda: status systemu
program
    .command('status')
    .description('Pokaż status systemu')
    .action(async () => {
        const agent = await initializeSystem();
        const status = agent.getSystemStatus();
        
        console.log('\n📊 Status Systemu RovoDev:');
        console.log('================================');
        console.log(`🎯 Aktywne zadania: ${status.activeTasks}`);
        console.log(`✅ Ukończone zadania: ${status.completedTasks}`);
        console.log(`🤖 Zarejestrowane agenty: ${status.agents.length}`);
        console.log(`🔗 Status MCP: ${status.mcpStatus.connected ? '✅ Połączony' : '❌ Rozłączony'}`);
        console.log(`🏢 Status Atlassian: ${status.atlassianStatus.connected ? '✅ Połączony' : '❌ Rozłączony'}`);
        console.log(`🚀 Status RovoDev: ${status.rovoDevStatus.connected ? '✅ Połączony' : '❌ Rozłączony'}`);
        
        if (status.taskManager) {
            console.log('\n📋 Task Manager:');
            console.log(`   Pending: ${status.taskManager.pending}`);
            console.log(`   Running: ${status.taskManager.running}`);
            console.log(`   Completed: ${status.taskManager.completed}`);
            console.log(`   Failed: ${status.taskManager.failed}`);
        }
    });

// Komenda: lista zadań
program
    .command('tasks')
    .description('Pokaż listę zadań')
    .option('-s, --status <status>', 'Filtruj po statusie (pending|running|completed|failed)')
    .action(async (options) => {
        await initializeSystem();
        const tasks = taskManager.getAllTasks();
        
        let filteredTasks = tasks;
        if (options.status) {
            filteredTasks = tasks.filter(task => task.status === options.status);
        }
        
        console.log('\n📋 Lista Zadań:');
        console.log('================');
        
        if (filteredTasks.length === 0) {
            console.log('Brak zadań do wyświetlenia.');
            return;
        }
        
        filteredTasks.forEach(task => {
            const statusIcon = {
                pending: '⏳',
                running: '🔄',
                completed: '✅',
                failed: '❌'
            }[task.status] || '❓';
            
            console.log(`${statusIcon} ${task.id}: ${task.title}`);
            console.log(`   Status: ${task.status}`);
            console.log(`   Priorytet: ${task.priority}`);
            if (task.dependencies?.length > 0) {
                console.log(`   Zależności: ${task.dependencies.join(', ')}`);
            }
            console.log('');
        });
    });

// Komenda: utwórz zadanie
program
    .command('create-task')
    .description('Utwórz nowe zadanie')
    .option('-t, --title <title>', 'Tytuł zadania')
    .option('-d, --description <description>', 'Opis zadania')
    .option('-p, --priority <priority>', 'Priorytet (high|medium|low)', 'medium')
    .option('--type <type>', 'Typ zadania', 'development')
    .option('--deps <dependencies>', 'Zależności (oddzielone przecinkami)')
    .option('--reqs <requirements>', 'Wymagania (oddzielone przecinkami)')
    .action(async (options) => {
        const agent = await initializeSystem();
        
        if (!options.title) {
            console.error('❌ Tytuł zadania jest wymagany. Użyj --title');
            process.exit(1);
        }
        
        const taskData = {
            title: options.title,
            description: options.description || '',
            priority: options.priority,
            type: options.type,
            dependencies: options.deps ? options.deps.split(',').map(d => d.trim()) : [],
            requirements: options.reqs ? options.reqs.split(',').map(r => r.trim()) : []
        };
        
        try {
            const taskId = await agent.createTask(taskData);
            console.log(`✅ Utworzono zadanie: ${taskId}`);
            console.log(`📋 Tytuł: ${taskData.title}`);
            console.log(`🎯 Priorytet: ${taskData.priority}`);
        } catch (error) {
            console.error('❌ Błąd tworzenia zadania:', error.message);
        }
    });

// Komenda: wykonaj zadanie
program
    .command('execute-task <taskId>')
    .description('Wykonaj konkretne zadanie')
    .action(async (taskId) => {
        const agent = await initializeSystem();
        
        try {
            const task = taskManager.getTask(taskId);
            if (!task) {
                console.error(`❌ Nie znaleziono zadania: ${taskId}`);
                process.exit(1);
            }
            
            console.log(`🔄 Wykonywanie zadania: ${task.title}`);
            const result = await agent.executeTask(task);
            
            console.log('✅ Zadanie wykonane pomyślnie!');
            console.log('📊 Wynik:', JSON.stringify(result, null, 2));
        } catch (error) {
            console.error('❌ Błąd wykonania zadania:', error.message);
        }
    });

// Komenda: przeszukaj internet
program
    .command('search <query>')
    .description('Przeszukaj internet używając Brave Search')
    .option('-c, --count <count>', 'Liczba wyników', '5')
    .action(async (query, options) => {
        await initializeSystem();
        
        try {
            console.log(`🔍 Przeszukiwanie: "${query}"`);
            
            const searchResult = await mcpManager.callTool('brave-search', 'brave_web_search', {
                query: query,
                count: parseInt(options.count)
            });
            
            console.log('\n📰 Wyniki wyszukiwania:');
            console.log('========================');
            
            if (searchResult.content && searchResult.content[0]) {
                const results = JSON.parse(searchResult.content[0].text);
                
                if (results.web && results.web.results) {
                    results.web.results.forEach((result, index) => {
                        console.log(`${index + 1}. ${result.title}`);
                        console.log(`   ${result.url}`);
                        console.log(`   ${result.description}`);
                        console.log('');
                    });
                }
            }
        } catch (error) {
            console.error('❌ Błąd wyszukiwania:', error.message);
        }
    });

// Komenda: sprawdź wiadomości wp.pl
program
    .command('news-wp')
    .description('Sprawdź najnowsze wiadomości z wp.pl')
    .action(async () => {
        await initializeSystem();
        
        try {
            console.log('📰 Pobieranie najnowszych wiadomości z wp.pl...');
            
            // Użyj Puppeteer do pobrania wiadomości
            const pageResult = await mcpManager.callTool('puppeteer', 'screenshot', {
                url: 'https://wiadomosci.wp.pl',
                fullPage: false
            });
            
            // Alternatywnie użyj Brave Search do wyszukania najnowszych wiadomości
            const searchResult = await mcpManager.callTool('brave-search', 'brave_web_search', {
                query: 'site:wp.pl najnowsze wiadomości',
                count: 10
            });
            
            console.log('\n📰 Najnowsze wiadomości z wp.pl:');
            console.log('==================================');
            
            if (searchResult.content && searchResult.content[0]) {
                const results = JSON.parse(searchResult.content[0].text);
                
                if (results.web && results.web.results) {
                    results.web.results.forEach((result, index) => {
                        console.log(`${index + 1}. ${result.title}`);
                        console.log(`   ${result.url}`);
                        console.log(`   ${result.description}`);
                        console.log('');
                    });
                }
            }
        } catch (error) {
            console.error('❌ Błąd pobierania wiadomości:', error.message);
        }
    });

// Komenda: konfiguracja MCP
program
    .command('mcp')
    .description('Zarządzanie serwerami MCP')
    .option('--list', 'Pokaż listę serwerów')
    .option('--status', 'Pokaż status połączeń')
    .option('--tools', 'Pokaż dostępne narzędzia')
    .action(async (options) => {
        await initializeSystem();
        
        if (options.list) {
            const servers = mcpManager.getConfiguredServers();
            console.log('\n🔗 Skonfigurowane serwery MCP:');
            console.log('===============================');
            servers.forEach(server => {
                console.log(`• ${server}`);
            });
        }
        
        if (options.status) {
            const status = mcpManager.getStatus();
            console.log('\n📊 Status serwerów MCP:');
            console.log('========================');
            console.log(`Połączone: ${status.connected ? '✅' : '❌'}`);
            console.log(`Aktywne serwery: ${status.activeServers}`);
            console.log(`Dostępne narzędzia: ${status.availableTools}`);
        }
        
        if (options.tools) {
            const tools = mcpManager.getAllAvailableTools();
            console.log('\n🛠️ Dostępne narzędzia MCP:');
            console.log('===========================');
            Object.entries(tools).forEach(([server, serverTools]) => {
                console.log(`\n📦 ${server}:`);
                serverTools.forEach(tool => {
                    console.log(`  • ${tool.name}: ${tool.description || 'Brak opisu'}`);
                });
            });
        }
    });

// Komenda: interaktywny tryb
program
    .command('interactive')
    .description('Uruchom tryb interaktywny')
    .action(async () => {
        const agent = await initializeSystem();
        
        console.log('\n🎯 Tryb interaktywny RovoDev CLI');
        console.log('================================');
        console.log('Dostępne komendy:');
        console.log('• status - status systemu');
        console.log('• tasks - lista zadań');
        console.log('• search <query> - wyszukaj w internecie');
        console.log('• news - najnowsze wiadomości');
        console.log('• exit - wyjście');
        console.log('');
        
        const readline = await import('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: 'rovodev> '
        });
        
        rl.prompt();
        
        rl.on('line', async (line) => {
            const input = line.trim();
            
            if (input === 'exit') {
                rl.close();
                return;
            }
            
            if (input === 'status') {
                const status = agent.getSystemStatus();
                console.log(`🎯 Aktywne: ${status.activeTasks}, ✅ Ukończone: ${status.completedTasks}`);
            } else if (input === 'tasks') {
                const tasks = taskManager.getAllTasks();
                console.log(`📋 Łącznie zadań: ${tasks.length}`);
            } else if (input.startsWith('search ')) {
                const query = input.substring(7);
                try {
                    const result = await mcpManager.callTool('brave-search', 'brave_web_search', {
                        query: query,
                        count: 3
                    });
                    console.log(`🔍 Znaleziono wyniki dla: "${query}"`);
                } catch (error) {
                    console.log('❌ Błąd wyszukiwania');
                }
            } else if (input === 'news') {
                console.log('📰 Pobieranie najnowszych wiadomości...');
                // Implementacja pobierania wiadomości
            } else if (input) {
                console.log('❓ Nieznana komenda. Wpisz "exit" aby wyjść.');
            }
            
            rl.prompt();
        });
        
        rl.on('close', () => {
            console.log('\n👋 Do widzenia!');
            process.exit(0);
        });
    });

// Obsługa błędów
program.on('command:*', () => {
    console.error('❌ Nieznana komenda. Użyj --help aby zobaczyć dostępne opcje.');
    process.exit(1);
});

// Uruchomienie CLI
program.parse();