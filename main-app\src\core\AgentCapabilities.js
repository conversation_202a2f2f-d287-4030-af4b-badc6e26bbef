/**
 * <PERSON><PERSON><PERSON><PERSON> możliwości agentów AI
 */

export const AgentCapabilities = {
    // Planning Agent Capabilities
    PLANNING: {
        STRATEGIC_PLANNING: 'strategic_planning',
        TASK_DECOMPOSITION: 'task_decomposition',
        RESOURCE_ALLOCATION: 'resource_allocation',
        TIMELINE_ESTIMATION: 'timeline_estimation',
        RISK_ASSESSMENT: 'risk_assessment',
        DEPENDENCY_ANALYSIS: 'dependency_analysis',
        MILESTONE_DEFINITION: 'milestone_definition',
        CONTINGENCY_PLANNING: 'contingency_planning',
        PRIORITY_MATRIX: 'priority_matrix',
        WORKLOAD_BALANCING: 'workload_balancing'
    },

    // Research Agent Capabilities
    RESEARCH: {
        WEB_RESEARCH: 'web_research',
        ACADEMIC_RESEARCH: 'academic_research',
        MARKET_ANALYSIS: 'market_analysis',
        COMPETITIVE_INTELLIGENCE: 'competitive_intelligence',
        TECHNOLOGY_SCOUTING: 'technology_scouting',
        TREND_ANALYSIS: 'trend_analysis',
        DATA_MINING: 'data_mining',
        INFORMATION_SYNTHESIS: 'information_synthesis',
        SOURCE_VERIFICATION: 'source_verification',
        KNOWLEDGE_EXTRACTION: 'knowledge_extraction'
    },

    // Analysis Agent Capabilities
    ANALYSIS: {
        REQUIREMENTS_ANALYSIS: 'requirements_analysis',
        FEASIBILITY_STUDY: 'feasibility_study',
        IMPACT_ANALYSIS: 'impact_analysis',
        COST_BENEFIT_ANALYSIS: 'cost_benefit_analysis',
        TECHNICAL_ANALYSIS: 'technical_analysis',
        BUSINESS_ANALYSIS: 'business_analysis',
        DATA_ANALYSIS: 'data_analysis',
        PATTERN_RECOGNITION: 'pattern_recognition',
        DECISION_SUPPORT: 'decision_support',
        RECOMMENDATION_ENGINE: 'recommendation_engine'
    },

    // Coding Agent Capabilities
    CODING: {
        ARCHITECTURE_DESIGN: 'architecture_design',
        CODE_GENERATION: 'code_generation',
        CODE_REVIEW: 'code_review',
        REFACTORING: 'refactoring',
        TESTING_STRATEGY: 'testing_strategy',
        PERFORMANCE_OPTIMIZATION: 'performance_optimization',
        SECURITY_ANALYSIS: 'security_analysis',
        DOCUMENTATION_GENERATION: 'documentation_generation',
        API_DESIGN: 'api_design',
        DATABASE_DESIGN: 'database_design',
        DEPLOYMENT_STRATEGY: 'deployment_strategy',
        MONITORING_SETUP: 'monitoring_setup'
    },

    // Cross-Agent Capabilities
    COLLABORATION: {
        KNOWLEDGE_SHARING: 'knowledge_sharing',
        CONTEXT_PASSING: 'context_passing',
        RESULT_VALIDATION: 'result_validation',
        PEER_REVIEW: 'peer_review',
        CONSENSUS_BUILDING: 'consensus_building',
        CONFLICT_RESOLUTION: 'conflict_resolution'
    },

    // AI Enhancement Capabilities
    AI_ENHANCEMENT: {
        MACHINE_LEARNING: 'machine_learning',
        NATURAL_LANGUAGE_PROCESSING: 'natural_language_processing',
        COMPUTER_VISION: 'computer_vision',
        PREDICTIVE_ANALYTICS: 'predictive_analytics',
        ANOMALY_DETECTION: 'anomaly_detection',
        RECOMMENDATION_SYSTEMS: 'recommendation_systems',
        AUTOMATED_REASONING: 'automated_reasoning',
        KNOWLEDGE_GRAPHS: 'knowledge_graphs'
    }
};

export const CapabilityLevels = {
    BASIC: 'basic',
    INTERMEDIATE: 'intermediate',
    ADVANCED: 'advanced',
    EXPERT: 'expert'
};

export const CapabilityDomains = {
    TECHNICAL: 'technical',
    BUSINESS: 'business',
    CREATIVE: 'creative',
    ANALYTICAL: 'analytical',
    STRATEGIC: 'strategic'
};

/**
 * Mapa możliwości agentów z poziomami kompetencji
 */
export const AgentCapabilityMatrix = {
    planning: {
        primary: [
            { capability: AgentCapabilities.PLANNING.STRATEGIC_PLANNING, level: CapabilityLevels.EXPERT },
            { capability: AgentCapabilities.PLANNING.TASK_DECOMPOSITION, level: CapabilityLevels.EXPERT },
            { capability: AgentCapabilities.PLANNING.RESOURCE_ALLOCATION, level: CapabilityLevels.ADVANCED },
            { capability: AgentCapabilities.PLANNING.TIMELINE_ESTIMATION, level: CapabilityLevels.ADVANCED },
            { capability: AgentCapabilities.PLANNING.RISK_ASSESSMENT, level: CapabilityLevels.ADVANCED }
        ],
        secondary: [
            { capability: AgentCapabilities.ANALYSIS.FEASIBILITY_STUDY, level: CapabilityLevels.INTERMEDIATE },
            { capability: AgentCapabilities.COLLABORATION.KNOWLEDGE_SHARING, level: CapabilityLevels.ADVANCED }
        ]
    },

    research: {
        primary: [
            { capability: AgentCapabilities.RESEARCH.WEB_RESEARCH, level: CapabilityLevels.EXPERT },
            { capability: AgentCapabilities.RESEARCH.INFORMATION_SYNTHESIS, level: CapabilityLevels.EXPERT },
            { capability: AgentCapabilities.RESEARCH.TECHNOLOGY_SCOUTING, level: CapabilityLevels.ADVANCED },
            { capability: AgentCapabilities.RESEARCH.TREND_ANALYSIS, level: CapabilityLevels.ADVANCED },
            { capability: AgentCapabilities.RESEARCH.SOURCE_VERIFICATION, level: CapabilityLevels.ADVANCED }
        ],
        secondary: [
            { capability: AgentCapabilities.ANALYSIS.DATA_ANALYSIS, level: CapabilityLevels.INTERMEDIATE },
            { capability: AgentCapabilities.AI_ENHANCEMENT.NATURAL_LANGUAGE_PROCESSING, level: CapabilityLevels.INTERMEDIATE }
        ]
    },

    analysis: {
        primary: [
            { capability: AgentCapabilities.ANALYSIS.REQUIREMENTS_ANALYSIS, level: CapabilityLevels.EXPERT },
            { capability: AgentCapabilities.ANALYSIS.TECHNICAL_ANALYSIS, level: CapabilityLevels.EXPERT },
            { capability: AgentCapabilities.ANALYSIS.DECISION_SUPPORT, level: CapabilityLevels.ADVANCED },
            { capability: AgentCapabilities.ANALYSIS.PATTERN_RECOGNITION, level: CapabilityLevels.ADVANCED },
            { capability: AgentCapabilities.ANALYSIS.RECOMMENDATION_ENGINE, level: CapabilityLevels.ADVANCED }
        ],
        secondary: [
            { capability: AgentCapabilities.PLANNING.DEPENDENCY_ANALYSIS, level: CapabilityLevels.INTERMEDIATE },
            { capability: AgentCapabilities.AI_ENHANCEMENT.PREDICTIVE_ANALYTICS, level: CapabilityLevels.INTERMEDIATE }
        ]
    },

    coding: {
        primary: [
            { capability: AgentCapabilities.CODING.ARCHITECTURE_DESIGN, level: CapabilityLevels.EXPERT },
            { capability: AgentCapabilities.CODING.CODE_GENERATION, level: CapabilityLevels.EXPERT },
            { capability: AgentCapabilities.CODING.CODE_REVIEW, level: CapabilityLevels.EXPERT },
            { capability: AgentCapabilities.CODING.PERFORMANCE_OPTIMIZATION, level: CapabilityLevels.ADVANCED },
            { capability: AgentCapabilities.CODING.SECURITY_ANALYSIS, level: CapabilityLevels.ADVANCED }
        ],
        secondary: [
            { capability: AgentCapabilities.ANALYSIS.TECHNICAL_ANALYSIS, level: CapabilityLevels.INTERMEDIATE },
            { capability: AgentCapabilities.AI_ENHANCEMENT.AUTOMATED_REASONING, level: CapabilityLevels.INTERMEDIATE }
        ]
    }
};

/**
 * Sprawdza czy agent ma określoną możliwość na wymaganym poziomie
 */
export function hasCapability(agentType, capability, requiredLevel = CapabilityLevels.BASIC) {
    const agentCapabilities = AgentCapabilityMatrix[agentType];
    if (!agentCapabilities) return false;

    const allCapabilities = [...agentCapabilities.primary, ...agentCapabilities.secondary];
    const found = allCapabilities.find(cap => cap.capability === capability);
    
    if (!found) return false;

    const levelOrder = [CapabilityLevels.BASIC, CapabilityLevels.INTERMEDIATE, CapabilityLevels.ADVANCED, CapabilityLevels.EXPERT];
    const foundLevelIndex = levelOrder.indexOf(found.level);
    const requiredLevelIndex = levelOrder.indexOf(requiredLevel);

    return foundLevelIndex >= requiredLevelIndex;
}

/**
 * Zwraca wszystkie możliwości agenta
 */
export function getAgentCapabilities(agentType) {
    return AgentCapabilityMatrix[agentType] || { primary: [], secondary: [] };
}