import { PlanningAgent } from '../agents/PlanningAgent.js';
import { ResearchAgent } from '../agents/ResearchAgent.js';
import { AnalysisAgent } from '../agents/AnalysisAgent.js';
import { CodingAgent } from '../agents/CodingAgent.js';
import { logger } from '../utils/logger.js';

/**
 * Registry zarządzający wszystkimi agentami w systemie
 */
export class AgentRegistry {
    constructor() {
        this.agents = new Map();
        this.agentConfigs = new Map();
    }

    async registerAgents() {
        logger.info('📝 Rejestrowanie agentów...');

        // Rejestracja Planning Agent
        const planningAgent = new PlanningAgent({
            id: 'planning',
            name: 'Planning Agent',
            description: 'Agent odpowiedzialny za wstępne planowanie zadań'
        });
        this.registerAgent('planning', planningAgent);

        // Rejestracja Research Agent
        const researchAgent = new ResearchAgent({
            id: 'research',
            name: 'Research Agent',
            description: 'Agent odpowiedzialny za badania i analizę zasobów'
        });
        this.registerAgent('research', researchAgent);

        // Rejestracja Analysis Agent
        const analysisAgent = new AnalysisAgent({
            id: 'analysis',
            name: 'Analysis Agent',
            description: 'Agent odpowiedzialny za podsumowanie i przekazanie instrukcji'
        });
        this.registerAgent('analysis', analysisAgent);

        // Rejestracja Coding Agent
        const codingAgent = new CodingAgent({
            id: 'coding',
            name: 'Coding Agent',
            description: 'Agent odpowiedzialny za implementację kodu'
        });
        this.registerAgent('coding', codingAgent);

        logger.info(`✅ Zarejestrowano ${this.agents.size} agentów`);
    }

    registerAgent(id, agent) {
        if (this.agents.has(id)) {
            throw new Error(`Agent o ID '${id}' już istnieje`);
        }

        this.agents.set(id, agent);
        this.agentConfigs.set(id, {
            id: agent.id,
            name: agent.name,
            description: agent.description,
            capabilities: agent.getCapabilities(),
            registeredAt: new Date()
        });

        logger.info(`📝 Zarejestrowano agenta: ${agent.name} (${id})`);
    }

    getAgent(id) {
        const agent = this.agents.get(id);
        if (!agent) {
            throw new Error(`Agent o ID '${id}' nie został znaleziony`);
        }
        return agent;
    }

    getRegisteredAgents() {
        return Array.from(this.agentConfigs.values());
    }

    async initializeAllAgents() {
        logger.info('🔄 Inicjalizacja wszystkich agentów...');
        
        for (const [id, agent] of this.agents) {
            try {
                await agent.initialize();
                logger.info(`✅ Zainicjalizowano agenta: ${id}`);
            } catch (error) {
                logger.error(`❌ Błąd inicjalizacji agenta ${id}:`, error);
                throw error;
            }
        }
    }

    async shutdownAllAgents() {
        logger.info('🛑 Zamykanie wszystkich agentów...');
        
        for (const [id, agent] of this.agents) {
            try {
                if (agent.shutdown) {
                    await agent.shutdown();
                }
                logger.info(`✅ Zamknięto agenta: ${id}`);
            } catch (error) {
                logger.error(`❌ Błąd zamykania agenta ${id}:`, error);
            }
        }
    }
}