@echo off
echo.
echo 🚀 Multi-Agent AI System - Quick Start
echo =====================================
echo.

cd /d "%~dp0"

echo 📍 Katalog roboczy: %CD%
echo.

echo 🔍 Sprawdzanie Node.js...
"C:\Program Files\nodejs\node.exe" --version
if errorlevel 1 (
    echo ❌ Node.js nie jest dostępny w standardowej lokalizacji
    echo 💡 Sprawdź czy Node.js jest zainstalowany w C:\Program Files\nodejs\
    pause
    exit /b 1
)
echo ✅ Node.js dostępny
echo.

echo 📦 Sprawdzanie zależności...
if not exist "node_modules" (
    echo 📥 Instalowanie zależności NPM...
    "C:\Program Files\nodejs\npm.cmd" install
    if errorlevel 1 (
        echo ❌ Błąd instalacji zależności
        pause
        exit /b 1
    )
    echo ✅ Zależności zainstalowane
) else (
    echo ✅ Zależności już zainstalowane
)
echo.

echo 🧪 Uruchamianie testu systemu...
echo.
"C:\Program Files\nodejs\node.exe" test-system.js

echo.
echo 🎉 Test zakończony!
echo.
echo 🚀 Aby uruchomić główną aplikację:
echo    "C:\Program Files\nodejs\node.exe" src/index.js
echo.
echo 📚 Aby uruchomić inne testy:
echo    "C:\Program Files\nodejs\node.exe" test-magic-mcp.js
echo    "C:\Program Files\nodejs\node.exe" test-rovo-integration.js
echo.
pause