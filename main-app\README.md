# Multi-Agent AI System

Zaawansowany system wieloagentowy z orkiestratorem SuperAgent, integracją MCP i zarządzaniem zadaniami.

## 🚀 Funkcje

- **SuperAgent Orchestrator** - Główny koordynator zarządzający przepływem pracy
- **<PERSON><PERSON><PERSON>** - Planning, Research, Analysis, Coding
- **Task Manager** - Kolejkowanie, priorytetyzacja i persystencja zadań
- **MCP Integration** - Serwery context7, 21st.dev i lokalne narzędzia
- **Atlassian Integration** - Integracja z Jira/Confluence
- **RovoDev Integration** - Zaplecze AI dla wsparcia kodowania

## 📦 Instalacja

```bash
# Klonuj repozytorium
git clone <repository-url>
cd multi-agent-ai-system/main-app

# Zainstaluj zależności
npm install

# Skopiuj konfigurację
cp .env.example .env
# Edytuj .env z własnymi kluczami API
```

## ⚙️ Konfiguracja

### Opcja 1: Plik .env
Skopiuj `.env.example` do `.env` i skonfiguruj:

```env
# Context7 MCP Server
CONTEXT7_API_KEY=your_context7_api_key_here
CONTEXT7_ENDPOINT=https://api.context7.com

# 21st.dev MCP Server
TWENTYFIRST_API_KEY=your_21st_dev_api_key_here
TWENTYFIRST_ENDPOINT=https://api.21st.dev

# 21st.dev Magic MCP Server (gotowy API key)
TWENTYFIRST_MAGIC_API_KEY=a2963c042498ab5d207844614351b4db01af277ca1689ef267381d99dbd8eb93

# Task Manager
TASK_PERSISTENCE_PATH=./data/tasks
MAX_CONCURRENT_TASKS=3
```

### Opcja 2: Plik mcp-config.json
Alternatywnie możesz skonfigurować serwery MCP przez plik `mcp-config.json`:

```json
{
  "mcpServers": {
    "@21st-dev/magic": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@21st-dev/magic@latest",
        "API_KEY=\"a2963c042498ab5d207844614351b4db01af277ca1689ef267381d99dbd8eb93\""
      ]
    }
  }
}
```

**Uwaga:** Serwer @21st-dev/magic ma już skonfigurowany API key i jest gotowy do użycia!

## 🏃‍♂️ Uruchomienie

```bash
# Uruchom system
npm start

# Tryb deweloperski
npm run dev

# Testy systemu
npm run test:system

# Test lokalnego serwera MCP
npm run test:mcp

# Test serwera @21st-dev/magic
npm run test:magic
```

## 🏗️ Architektura

### SuperAgent (Orchestrator)
Główny koordynator zarządzający przepływem pracy między agentami.

### Agenci Specjalistyczni
1. **Planning Agent** - Planowanie wstępne zadań
2. **Research Agent** - Badania, analiza zasobów
3. **Analysis Agent** - Podsumowanie i przekazanie instrukcji
4. **Coding Agent** - Implementacja kodu

### Task Manager
- Kolejkowanie zadań z priorytetyzacją
- Zarządzanie zależnościami między zadaniami
- Persystencja stanu zadań
- Retry logic dla nieudanych zadań
- Monitoring postępu

### MCP Integration
- **Context7 (Upstash)** - Zarządzanie kontekstem i pamięcią z Redis
- **21st.dev** - Zaawansowane funkcje AI
- **21st.dev Magic** - Magiczne narzędzia AI z gotowym API key
- **GitHub** - Integracja z repozytoriami, issues, pull requests
- **Browser Search** - Wyszukiwanie w internecie (Brave Search)
- **Puppeteer** - Automatyzacja przeglądarki i web scraping
- **Desktop Integration** - Dostęp do pulpitu użytkownika
- **Local Tools** - Operacje na plikach i systemie

## 📋 Przykład użycia

```javascript
import { SuperAgent } from './src/core/SuperAgent.js';
// ... inne importy

// Inicjalizacja systemu
const superAgent = new SuperAgent({
    mcpManager,
    atlassianIntegration,
    agentRegistry,
    taskManager
});

await superAgent.initialize();

// Tworzenie zadania
const taskId = await superAgent.createTask({
    title: 'Stwórz komponent React',
    description: 'Komponent do wyświetlania listy użytkowników',
    priority: 'high',
    type: 'development',
    requirements: [
        'Użyj TypeScript',
        'Dodaj paginację',
        'Implementuj wyszukiwanie'
    ]
});

// Monitoring statusu
const status = superAgent.getSystemStatus();
console.log('Status systemu:', status);
```

## 🔧 Narzędzia MCP

System obsługuje następujące narzędzia MCP:

### Context7 (Upstash)
- Zarządzanie kontekstem z Redis
- Pamięć długoterminowa
- Vector search
- Retrieval augmented generation

### 21st.dev
- Analiza kodu
- Optymalizacja
- Zaawansowane funkcje AI

### 21st.dev Magic
- Magiczne narzędzia AI
- Generowanie kodu
- Asystent AI
- Gotowy do użycia (z predefiniowanym API key)

### GitHub
- Zarządzanie repozytoriami
- Issues i pull requests
- GitHub API integration

### Browser Search (Brave)
- Wyszukiwanie w internecie
- Dostęp do aktualnych informacji
- Web research capabilities

### Puppeteer Browser
- Automatyzacja przeglądarki
- Web scraping
- Screenshots i PDF generation

### Desktop Integration
- Dostęp do pulpitu użytkownika
- Wyszukiwanie plików systemowych
- Integracja z systemem operacyjnym

### Local Tools
- `read_file` - Odczyt plików
- `write_file` - Zapis plików
- `list_directory` - Listowanie katalogów
- `get_system_info` - Informacje o systemie
- `execute_command` - Wykonywanie poleceń

### RovoDev Integration
- **Analiza kodu** - Jakość, bezpieczeństwo, wydajność
- **Generowanie kodu** - AI-assisted code generation
- **Code Review** - Automatyczne przeglądy kodu
- **Debugowanie** - Pomoc w rozwiązywaniu problemów
- **Dokumentacja** - Automatyczne generowanie docs
- **Optymalizacja** - Sugestie poprawy wydajności

## 📊 Monitoring

System zapewnia szczegółowy monitoring:

```javascript
// Status systemu
const systemStatus = superAgent.getSystemStatus();

// Status zadań
const taskStatus = taskManager.getStatus();

// Status MCP
const mcpStatus = mcpManager.getStatus();

// Dostępne narzędzia
const tools = mcpManager.getAllAvailableTools();
```

## 🧪 Testowanie

```bash
# Testy systemu
npm run test:system

# Testy jednostkowe
npm test

# Test serwera MCP
npm run test:mcp

# Test @21st-dev/magic MCP
npm run test:magic

# Test integracji RovoDev
npm run test:rovo
```

## 📁 Struktura projektu

```
main-app/
├── src/
│   ├── agents/           # Agenci specjalistyczni
│   ├── core/            # Główne komponenty
│   ├── mcp/             # Integracja MCP
│   ├── integrations/    # Integracje zewnętrzne
│   └── utils/           # Narzędzia pomocnicze
├── data/                # Dane i persystencja
├── logs/                # Logi systemu
└── test-data/           # Dane testowe
```

## 🤝 Rozwój

1. Fork repozytorium
2. Utwórz branch dla funkcji (`git checkout -b feature/AmazingFeature`)
3. Commit zmian (`git commit -m 'Add some AmazingFeature'`)
4. Push do branch (`git push origin feature/AmazingFeature`)
5. Otwórz Pull Request

## 📄 Licencja

MIT License - zobacz plik LICENSE dla szczegółów.