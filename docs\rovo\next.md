
Nowe zadania:

- Interfejs  poprzez cli rovodev do zarządzania zadaniami
- Więcej serwerów MCP (GitHub, Slack, etc.)
- Konfiguracja połączenia
- Implementacja klienta
    - Dodaj własne zadania przez API
    - Skonfiguruj dodatkowe serwery MCP
    - Rozszerz funkcjonalność agentów

    Połącz z rovodev oraz wykorzystaj zdolnosci przeszukiwania internetu. Sprawdz najnowsze wiadomosci na wp.pl i zwroc wiadomosc
.

Po zakonczeniu zadania uruchom aplikacje i stwórz razem z nią interfejs webowy.
Zaktualizuj zadania w dokumentacji