#!/usr/bin/env node

/**
 * Skrypt walidacji systemu - sprawdza czy wszystko jest poprawnie skonfigurowane
 */

import fs from 'fs/promises';
import path from 'path';
import { logger } from './src/utils/logger.js';

class SystemValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.checks = [];
    }

    async validate() {
        console.log('🔍 Rozpoczynam walidację systemu...\n');

        await this.checkNodeVersion();
        await this.checkPackageJson();
        await this.checkEnvironmentFile();
        await this.checkDirectories();
        await this.checkDependencies();
        await this.checkImports();

        this.printResults();
        return this.errors.length === 0;
    }

    async checkNodeVersion() {
        try {
            const version = process.version;
            const majorVersion = parseInt(version.slice(1).split('.')[0]);
            
            if (majorVersion >= 18) {
                this.addCheck('✅ Node.js version', `${version} (OK)`);
            } else if (majorVersion >= 14) {
                this.addWarning('⚠️ Node.js version', `${version} (zalecana 18+)`);
            } else {
                this.addError('❌ Node.js version', `${version} (wymagana 14+)`);
            }
        } catch (error) {
            this.addError('❌ Node.js', 'Nie można sprawdzić wersji');
        }
    }

    async checkPackageJson() {
        try {
            const packagePath = './package.json';
            const packageContent = await fs.readFile(packagePath, 'utf8');
            const packageJson = JSON.parse(packageContent);

            if (packageJson.type === 'module') {
                this.addCheck('✅ ES Modules', 'Skonfigurowane poprawnie');
            } else {
                this.addError('❌ ES Modules', 'Brak "type": "module" w package.json');
            }

            if (packageJson.main) {
                this.addCheck('✅ Main entry', packageJson.main);
            } else {
                this.addWarning('⚠️ Main entry', 'Brak głównego pliku');
            }

        } catch (error) {
            this.addError('❌ package.json', 'Nie można odczytać pliku');
        }
    }

    async checkEnvironmentFile() {
        try {
            await fs.access('.env');
            this.addCheck('✅ Environment file', '.env istnieje');
        } catch (error) {
            try {
                await fs.access('.env.example');
                this.addWarning('⚠️ Environment file', '.env nie istnieje, ale .env.example jest dostępny');
            } catch (error) {
                this.addError('❌ Environment file', 'Brak .env i .env.example');
            }
        }
    }

    async checkDirectories() {
        const requiredDirs = ['src', 'src/agents', 'src/core', 'src/mcp', 'src/integrations', 'src/utils'];
        const optionalDirs = ['logs', 'data', 'data/tasks'];

        for (const dir of requiredDirs) {
            try {
                await fs.access(dir);
                this.addCheck('✅ Directory', dir);
            } catch (error) {
                this.addError('❌ Directory', `Brak katalogu: ${dir}`);
            }
        }

        for (const dir of optionalDirs) {
            try {
                await fs.access(dir);
                this.addCheck('✅ Optional directory', dir);
            } catch (error) {
                this.addWarning('⚠️ Optional directory', `Katalog ${dir} zostanie utworzony automatycznie`);
            }
        }
    }

    async checkDependencies() {
        try {
            await fs.access('node_modules');
            this.addCheck('✅ Dependencies', 'node_modules istnieje');
        } catch (error) {
            this.addError('❌ Dependencies', 'Uruchom: npm install');
        }
    }

    async checkImports() {
        const coreFiles = [
            'src/index.js',
            'src/core/SuperAgent.js',
            'src/core/TaskManager.js',
            'src/core/AgentRegistry.js',
            'src/mcp/MCPManager.js',
            'src/utils/logger.js'
        ];

        for (const file of coreFiles) {
            try {
                await fs.access(file);
                this.addCheck('✅ Core file', file);
            } catch (error) {
                this.addError('❌ Core file', `Brak pliku: ${file}`);
            }
        }
    }

    addCheck(status, message) {
        this.checks.push({ status, message });
    }

    addError(status, message) {
        this.errors.push({ status, message });
    }

    addWarning(status, message) {
        this.warnings.push({ status, message });
    }

    printResults() {
        console.log('\n📋 WYNIKI WALIDACJI:\n');

        // Sprawdzenia OK
        if (this.checks.length > 0) {
            console.log('✅ SPRAWDZENIA POZYTYWNE:');
            this.checks.forEach(check => {
                console.log(`   ${check.status} ${check.message}`);
            });
            console.log();
        }

        // Ostrzeżenia
        if (this.warnings.length > 0) {
            console.log('⚠️  OSTRZEŻENIA:');
            this.warnings.forEach(warning => {
                console.log(`   ${warning.status} ${warning.message}`);
            });
            console.log();
        }

        // Błędy
        if (this.errors.length > 0) {
            console.log('❌ BŁĘDY DO NAPRAWY:');
            this.errors.forEach(error => {
                console.log(`   ${error.status} ${error.message}`);
            });
            console.log();
        }

        // Podsumowanie
        console.log('📊 PODSUMOWANIE:');
        console.log(`   ✅ Sprawdzenia: ${this.checks.length}`);
        console.log(`   ⚠️  Ostrzeżenia: ${this.warnings.length}`);
        console.log(`   ❌ Błędy: ${this.errors.length}`);
        console.log();

        if (this.errors.length === 0) {
            console.log('🎉 SYSTEM GOTOWY DO URUCHOMIENIA!');
        } else {
            console.log('🔧 NAPRAW BŁĘDY PRZED URUCHOMIENIEM');
            console.log('   Uruchom: fix_environment.bat');
        }
    }
}

// Uruchomienie walidacji
const validator = new SystemValidator();
validator.validate().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('❌ Błąd walidacji:', error);
    process.exit(1);
});