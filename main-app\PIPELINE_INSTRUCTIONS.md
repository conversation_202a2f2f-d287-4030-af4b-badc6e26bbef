# 🚀 Instrukcje Uruchomienia Pipeline Multi-Agent AI

## Wymagania Systemowe

### 1. Node.js i npm
```bash
# Sprawdź czy Node.js jest zainstalowany
node --version  # powinno pokazać v18+ lub v20+
npm --version   # powinno pokazać v8+

# Jeśli nie masz Node.js, pobierz z: https://nodejs.org/
```

### 2. Instalacja <PERSON>
```bash
cd main-app
npm install
```

## 🔄 Uruchomienie Pełnego Pipeline

### Opcja 1: Test Systemu (Podstawowy)
```bash
# Test podstawowych funkcji systemu
npm run test:system
# lub
node test-system.js
```

### Opcja 2: Test Pełnego Pipeline (Zaawansowany)
```bash
# Test kompletnego workflow: zadanie → analiza → research → planowanie → kodowanie
node tmp_rovodev_test_full_pipeline.js
```

### Opcja 3: Demo Pipeline (Symulacja)
```bash
# Symulacja działania pipeline bez rzeczywistego uruchomienia
node tmp_rovodev_pipeline_demo.js
```

### Opcja 4: Test Konkretnych Integracji
```bash
# Test integracji RovoDev
npm run test:rovo
# lub
node test-rovo-integration.js

# Test @21st-dev/magic MCP
npm run test:magic
# lub
node test-magic-mcp.js
```

## 📋 Struktura Pipeline

### Fazy Workflow "software_development":

1. **🎯 Requirements Gathering (Planning Agent)**
   - Analiza wymagań funkcjonalnych
   - Identyfikacja stakeholderów
   - Definicja kryteriów akceptacji
   - Oszacowanie złożoności

2. **🔍 Research and Analysis (Research Agent)**
   - Badanie technologii
   - Analiza konkurencji
   - Identyfikacja best practices
   - Ocena ryzyka

3. **🏗️ Architecture Design (Analysis Agent)**
   - Projektowanie architektury systemu
   - Projektowanie bazy danych
   - Specyfikacja API
   - Architektura bezpieczeństwa

4. **💻 Implementation (Coding Agent)**
   - Generowanie kodu
   - Testy jednostkowe
   - Testy integracyjne
   - Dokumentacja

5. **✅ Quality Assurance (Analysis Agent)**
   - Code review
   - Audit bezpieczeństwa
   - Testy wydajności
   - Sprawdzenie zgodności

## 🛠️ Konfiguracja

### 1. Zmienne Środowiskowe
```bash
# Skopiuj przykładową konfigurację
cp .env.example .env

# Edytuj .env i dodaj swoje klucze API:
# OPENAI_API_KEY=your_openai_key
# ANTHROPIC_API_KEY=your_anthropic_key
# ATLASSIAN_API_TOKEN=your_atlassian_token
```

### 2. Konfiguracja MCP
Edytuj `mcp-config.json` aby dostosować serwery MCP:
```json
{
  "servers": {
    "context7": {
      "command": "npx",
      "args": ["@context7/mcp-server"],
      "env": {}
    },
    "21st-dev": {
      "command": "npx",
      "args": ["@21st-dev/mcp-server"],
      "env": {}
    }
  }
}
```

## 📊 Monitoring Pipeline

### Podczas Wykonywania
Pipeline emituje eventy, które możesz monitorować:

```javascript
// Nasłuchiwanie eventów workflow
agentWorkflow.on('phase:started', (workflowId, phase) => {
    console.log(`🔄 Rozpoczęto fazę: ${phase.name}`);
});

agentWorkflow.on('phase:completed', (workflowId, phase, result) => {
    console.log(`✅ Ukończono fazę: ${phase.name}`);
});

agentWorkflow.on('workflow:completed', (workflowId, workflow, results) => {
    console.log(`🎉 Workflow ukończony: ${workflow.name}`);
});
```

### Status Systemu
```javascript
// Sprawdzenie statusu systemu
const status = superAgent.getSystemStatus();
console.log('Status:', status);

// Status konkretnego workflow
const workflowStatus = superAgent.getWorkflowStatus(workflowId);
console.log('Workflow:', workflowStatus);
```

## 🎯 Przykłady Użycia

### 1. Prosty Pipeline
```javascript
// Utworzenie i wykonanie workflow
const workflowId = await superAgent.executeWorkflow('software_development', {
    name: 'Moja Aplikacja',
    context: {
        technology: 'React',
        complexity: 'medium'
    }
});
```

### 2. Niestandardowy Pipeline
```javascript
// Niestandardowe zadanie
const taskId = await superAgent.createTask({
    title: 'API dla e-commerce',
    description: 'Stwórz REST API dla sklepu internetowego',
    priority: 'high',
    requirements: [
        'Użyj Node.js + Express',
        'MongoDB jako baza danych',
        'JWT authentication',
        'Swagger dokumentacja'
    ]
});
```

## 🐛 Rozwiązywanie Problemów

### Problem: Node.js nie jest rozpoznawany
```bash
# Windows - dodaj Node.js do PATH lub użyj pełnej ścieżki
"C:\Program Files\nodejs\node.exe" test-system.js

# Linux/Mac - sprawdź instalację
which node
npm config get prefix
```

### Problem: Błędy modułów ES
```bash
# Upewnij się, że package.json ma:
"type": "module"

# Lub użyj rozszerzenia .mjs
node test-system.mjs
```

### Problem: Brak kluczy API
```bash
# Sprawdź czy .env jest skonfigurowany
cat .env

# Dodaj brakujące klucze
echo "OPENAI_API_KEY=your_key" >> .env
```

## 📚 Dodatkowe Zasoby

- [README.md](./README.md) - Ogólna dokumentacja
- [QUICK_START.md](./QUICK_START.md) - Szybki start
- [docs/API_DOCUMENTATION.md](./docs/API_DOCUMENTATION.md) - Dokumentacja API
- [progress.md](../progress.md) - Status projektu
- [tasks.md](../tasks.md) - Lista zadań

## 🎉 Sukces!

Po pomyślnym uruchomieniu pipeline powinieneś zobaczyć:
- ✅ Wszystkie fazy ukończone
- 📦 Wygenerowane deliverables
- 💡 Rekomendacje dla każdej fazy
- 📊 Metryki jakości i wydajności
- 🎯 Kompletny kod aplikacji

Pipeline Multi-Agent AI jest gotowy do pracy! 🚀