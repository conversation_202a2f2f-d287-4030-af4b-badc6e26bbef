{"name": "multi-agent-ai-system", "version": "1.0.0", "description": "Advanced Multi-Agent AI System with SuperAgent Orchestrator", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:system": "node test-system.js", "test:mcp": "node src/mcp/servers/local-tools-server.js", "test:magic": "node test-magic-mcp.js", "test:rovo": "node test-rovo-integration.js", "build": "npm run build:agents && npm run build:mcp", "build:agents": "node scripts/build-agents.js", "build:mcp": "node scripts/build-mcp.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "@atlassian/jira-pi-client": "^1.0.0", "express": "^4.18.2", "ws": "^8.14.2", "axios": "^1.6.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "winston": "^3.11.0", "joi": "^17.11.0", "lodash": "^4.17.21"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "@types/node": "^20.8.0", "eslint": "^8.51.0"}, "keywords": ["ai", "agents", "mcp", "atlassian", "automation"], "author": "Your Name", "license": "MIT"}