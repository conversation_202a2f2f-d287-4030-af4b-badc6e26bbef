#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { execSync } from 'child_process';

/**
 * Lokalny serwer narzędzi MCP
 */
class LocalToolsServer {
    constructor() {
        this.server = new Server(
            {
                name: 'local-tools-server',
                version: '1.0.0'
            },
            {
                capabilities: {
                    tools: {},
                    resources: {}
                }
            }
        );

        this.setupToolHandlers();
        this.setupResourceHandlers();
    }

    setupToolHandlers() {
        // Narzędzie do operacji na plikach
        this.server.setRequestHandler('tools/list', async () => {
            return {
                tools: [
                    {
                        name: 'read_file',
                        description: 'Odczytuje zawartość pliku',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                path: {
                                    type: 'string',
                                    description: 'Ścieżka do pliku'
                                }
                            },
                            required: ['path']
                        }
                    },
                    {
                        name: 'write_file',
                        description: 'Zapisuje zawartość do pliku',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                path: {
                                    type: 'string',
                                    description: 'Ścieżka do pliku'
                                },
                                content: {
                                    type: 'string',
                                    description: 'Zawartość do zapisania'
                                }
                            },
                            required: ['path', 'content']
                        }
                    },
                    {
                        name: 'list_directory',
                        description: 'Listuje zawartość katalogu',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                path: {
                                    type: 'string',
                                    description: 'Ścieżka do katalogu'
                                }
                            },
                            required: ['path']
                        }
                    },
                    {
                        name: 'get_system_info',
                        description: 'Pobiera informacje o systemie',
                        inputSchema: {
                            type: 'object',
                            properties: {}
                        }
                    },
                    {
                        name: 'execute_command',
                        description: 'Wykonuje polecenie systemowe',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                command: {
                                    type: 'string',
                                    description: 'Polecenie do wykonania'
                                },
                                timeout: {
                                    type: 'number',
                                    description: 'Timeout w milisekundach',
                                    default: 30000
                                }
                            },
                            required: ['command']
                        }
                    }
                ]
            };
        });

        this.server.setRequestHandler('tools/call', async (request) => {
            const { name, arguments: args } = request.params;

            switch (name) {
                case 'read_file':
                    return await this.readFile(args.path);
                
                case 'write_file':
                    return await this.writeFile(args.path, args.content);
                
                case 'list_directory':
                    return await this.listDirectory(args.path);
                
                case 'get_system_info':
                    return await this.getSystemInfo();
                
                case 'execute_command':
                    return await this.executeCommand(args.command, args.timeout);
                
                default:
                    throw new Error(`Nieznane narzędzie: ${name}`);
            }
        });
    }

    setupResourceHandlers() {
        this.server.setRequestHandler('resources/list', async () => {
            return {
                resources: [
                    {
                        uri: 'file://system/info',
                        name: 'System Information',
                        description: 'Informacje o systemie operacyjnym',
                        mimeType: 'application/json'
                    },
                    {
                        uri: 'file://project/structure',
                        name: 'Project Structure',
                        description: 'Struktura projektu',
                        mimeType: 'text/plain'
                    }
                ]
            };
        });

        this.server.setRequestHandler('resources/read', async (request) => {
            const { uri } = request.params;

            switch (uri) {
                case 'file://system/info':
                    return {
                        contents: [{
                            uri,
                            mimeType: 'application/json',
                            text: JSON.stringify(await this.getSystemInfo(), null, 2)
                        }]
                    };
                
                case 'file://project/structure':
                    return {
                        contents: [{
                            uri,
                            mimeType: 'text/plain',
                            text: await this.getProjectStructure()
                        }]
                    };
                
                default:
                    throw new Error(`Nieznany zasób: ${uri}`);
            }
        });
    }

    async readFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            return {
                content: [{
                    type: 'text',
                    text: `Zawartość pliku ${filePath}:\n\n${content}`
                }]
            };
        } catch (error) {
            throw new Error(`Nie udało się odczytać pliku ${filePath}: ${error.message}`);
        }
    }

    async writeFile(filePath, content) {
        try {
            await fs.writeFile(filePath, content, 'utf8');
            return {
                content: [{
                    type: 'text',
                    text: `Pomyślnie zapisano plik: ${filePath}`
                }]
            };
        } catch (error) {
            throw new Error(`Nie udało się zapisać pliku ${filePath}: ${error.message}`);
        }
    }

    async listDirectory(dirPath) {
        try {
            const items = await fs.readdir(dirPath, { withFileTypes: true });
            const listing = items.map(item => ({
                name: item.name,
                type: item.isDirectory() ? 'directory' : 'file',
                path: path.join(dirPath, item.name)
            }));

            return {
                content: [{
                    type: 'text',
                    text: `Zawartość katalogu ${dirPath}:\n\n${JSON.stringify(listing, null, 2)}`
                }]
            };
        } catch (error) {
            throw new Error(`Nie udało się wylistować katalogu ${dirPath}: ${error.message}`);
        }
    }

    async getSystemInfo() {
        return {
            platform: os.platform(),
            arch: os.arch(),
            release: os.release(),
            hostname: os.hostname(),
            uptime: os.uptime(),
            memory: {
                total: os.totalmem(),
                free: os.freemem()
            },
            cpus: os.cpus().length,
            nodeVersion: process.version,
            timestamp: new Date().toISOString()
        };
    }

    async executeCommand(command, timeout = 30000) {
        try {
            const output = execSync(command, {
                timeout,
                encoding: 'utf8',
                maxBuffer: 1024 * 1024 // 1MB
            });

            return {
                content: [{
                    type: 'text',
                    text: `Wynik polecenia "${command}":\n\n${output}`
                }]
            };
        } catch (error) {
            throw new Error(`Błąd wykonania polecenia "${command}": ${error.message}`);
        }
    }

    async getProjectStructure() {
        try {
            const output = execSync('find . -type f -name "*.js" -o -name "*.json" -o -name "*.md" | head -50', {
                encoding: 'utf8',
                cwd: process.cwd()
            });

            return `Struktura projektu:\n\n${output}`;
        } catch (error) {
            return `Nie udało się pobrać struktury projektu: ${error.message}`;
        }
    }

    async start() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('Local Tools MCP Server uruchomiony');
    }
}

// Uruchom serwer
const server = new LocalToolsServer();
server.start().catch(console.error);