import express from 'express';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import path from 'path';
import { fileURLToPath } from 'url';
import { SuperAgent } from '../core/SuperAgent.js';
import { AgentRegistry } from '../core/AgentRegistry.js';
import { TaskManager } from '../core/TaskManager.js';
import { MCPManager } from '../mcp/MCPManager.js';
import { AtlassianIntegration } from '../integrations/AtlassianIntegration.js';
import { RovoDevIntegration } from '../integrations/RovoDevIntegration.js';
import { logger } from '../utils/logger.js';
import dotenv from 'dotenv';

// Załaduj konfigurację
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class RovoDevWebServer {
    constructor() {
        this.app = express();
        this.server = createServer(this.app);
        this.wss = new WebSocketServer({ server: this.server });
        this.port = process.env.WEB_PORT || 3000;
        this.clients = new Set();
        
        // Komponenty systemu
        this.superAgent = null;
        this.taskManager = null;
        this.mcpManager = null;
    }

    async initialize() {
        logger.info('🌐 Inicjalizacja RovoDev Web Server...');

        // Inicjalizacja komponentów systemu
        await this.initializeSystemComponents();
        
        // Konfiguracja Express
        this.setupExpress();
        
        // Konfiguracja WebSocket
        this.setupWebSocket();
        
        // Konfiguracja API routes
        this.setupRoutes();
        
        logger.info('✅ RovoDev Web Server zainicjalizowany');
    }

    async initializeSystemComponents() {
        try {
            // Inicjalizacja komponentów
            this.mcpManager = new MCPManager();
            const atlassianIntegration = new AtlassianIntegration();
            const rovoDevIntegration = new RovoDevIntegration();
            const agentRegistry = new AgentRegistry();
            this.taskManager = new TaskManager();
            
            // Inicjalizacja SuperAgent
            this.superAgent = new SuperAgent({
                mcpManager: this.mcpManager,
                atlassianIntegration,
                rovoDevIntegration,
                agentRegistry,
                taskManager: this.taskManager
            });

            // Rejestracja agentów
            await agentRegistry.registerAgents();
            
            // Inicjalizacja komponentów
            await this.taskManager.initialize();
            await rovoDevIntegration.initialize();
            await this.superAgent.initialize();
            
            // Nasłuchiwanie zdarzeń dla WebSocket
            this.setupSystemEventListeners();
            
        } catch (error) {
            logger.error('❌ Błąd inicjalizacji komponentów systemu:', error);
            throw error;
        }
    }

    setupExpress() {
        // Middleware
        this.app.use(express.json());
        this.app.use(express.static(path.join(__dirname, 'public')));
        
        // CORS
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
            
            if (req.method === 'OPTIONS') {
                res.sendStatus(200);
            } else {
                next();
            }
        });
    }

    setupWebSocket() {
        this.wss.on('connection', (ws) => {
            logger.info('🔌 Nowe połączenie WebSocket');
            this.clients.add(ws);
            
            // Wyślij aktualny status systemu
            this.sendToClient(ws, {
                type: 'system_status',
                data: this.superAgent.getSystemStatus()
            });
            
            ws.on('message', async (message) => {
                try {
                    const data = JSON.parse(message);
                    await this.handleWebSocketMessage(ws, data);
                } catch (error) {
                    logger.error('❌ Błąd przetwarzania wiadomości WebSocket:', error);
                    this.sendToClient(ws, {
                        type: 'error',
                        message: 'Błąd przetwarzania wiadomości'
                    });
                }
            });
            
            ws.on('close', () => {
                logger.info('🔌 Zamknięto połączenie WebSocket');
                this.clients.delete(ws);
            });
        });
    }

    async handleWebSocketMessage(ws, data) {
        switch (data.type) {
            case 'get_status':
                this.sendToClient(ws, {
                    type: 'system_status',
                    data: this.superAgent.getSystemStatus()
                });
                break;
                
            case 'get_tasks':
                this.sendToClient(ws, {
                    type: 'tasks_list',
                    data: this.taskManager.getAllTasks()
                });
                break;
                
            case 'create_task':
                try {
                    const taskId = await this.superAgent.createTask(data.task);
                    this.sendToClient(ws, {
                        type: 'task_created',
                        data: { taskId, task: data.task }
                    });
                } catch (error) {
                    this.sendToClient(ws, {
                        type: 'error',
                        message: `Błąd tworzenia zadania: ${error.message}`
                    });
                }
                break;
                
            case 'search_web':
                try {
                    const searchResult = await this.mcpManager.callTool('brave-search', 'brave_web_search', {
                        query: data.query,
                        count: data.count || 5
                    });
                    
                    this.sendToClient(ws, {
                        type: 'search_results',
                        data: searchResult
                    });
                } catch (error) {
                    this.sendToClient(ws, {
                        type: 'error',
                        message: `Błąd wyszukiwania: ${error.message}`
                    });
                }
                break;
        }
    }

    setupSystemEventListeners() {
        // Nasłuchuj zdarzeń TaskManager
        this.taskManager.on('task:created', (taskId, task) => {
            this.broadcastToClients({
                type: 'task_created',
                data: { taskId, task }
            });
        });
        
        this.taskManager.on('task:started', (taskId, task) => {
            this.broadcastToClients({
                type: 'task_started',
                data: { taskId, task }
            });
        });
        
        this.taskManager.on('task:completed', (taskId, result) => {
            this.broadcastToClients({
                type: 'task_completed',
                data: { taskId, result }
            });
        });
        
        this.taskManager.on('task:failed', (taskId, error) => {
            this.broadcastToClients({
                type: 'task_failed',
                data: { taskId, error: error.message }
            });
        });
        
        // Nasłuchuj zdarzeń SuperAgent
        this.superAgent.on('task:started', (taskId) => {
            this.broadcastToClients({
                type: 'agent_task_started',
                data: { taskId }
            });
        });
        
        this.superAgent.on('task:completed', (taskId, result) => {
            this.broadcastToClients({
                type: 'agent_task_completed',
                data: { taskId, result }
            });
        });
    }

    setupRoutes() {
        // Główna strona
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });
        
        // API: Status systemu
        this.app.get('/api/status', (req, res) => {
            try {
                const status = this.superAgent.getSystemStatus();
                res.json({ success: true, data: status });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // API: Lista zadań
        this.app.get('/api/tasks', (req, res) => {
            try {
                const tasks = this.taskManager.getAllTasks();
                res.json({ success: true, data: tasks });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // API: Utwórz zadanie
        this.app.post('/api/tasks', async (req, res) => {
            try {
                const taskId = await this.superAgent.createTask(req.body);
                res.json({ success: true, data: { taskId } });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // API: Wykonaj zadanie
        this.app.post('/api/tasks/:taskId/execute', async (req, res) => {
            try {
                const task = this.taskManager.getTask(req.params.taskId);
                if (!task) {
                    return res.status(404).json({ success: false, error: 'Zadanie nie znalezione' });
                }
                
                const result = await this.superAgent.executeTask(task);
                res.json({ success: true, data: result });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // API: Wyszukiwanie w internecie
        this.app.post('/api/search', async (req, res) => {
            try {
                const { query, count = 5 } = req.body;
                
                const searchResult = await this.mcpManager.callTool('brave-search', 'brave_web_search', {
                    query,
                    count
                });
                
                res.json({ success: true, data: searchResult });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // API: Najnowsze wiadomości wp.pl
        this.app.get('/api/news/wp', async (req, res) => {
            try {
                const searchResult = await this.mcpManager.callTool('brave-search', 'brave_web_search', {
                    query: 'site:wp.pl najnowsze wiadomości',
                    count: 10
                });
                
                res.json({ success: true, data: searchResult });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // API: Serwery MCP
        this.app.get('/api/mcp/servers', (req, res) => {
            try {
                const servers = this.mcpManager.getConfiguredServers();
                const status = this.mcpManager.getStatus();
                res.json({ success: true, data: { servers, status } });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // API: Narzędzia MCP
        this.app.get('/api/mcp/tools', (req, res) => {
            try {
                const tools = this.mcpManager.getAllAvailableTools();
                res.json({ success: true, data: tools });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
    }

    sendToClient(ws, message) {
        if (ws.readyState === ws.OPEN) {
            ws.send(JSON.stringify(message));
        }
    }

    broadcastToClients(message) {
        const messageStr = JSON.stringify(message);
        this.clients.forEach(client => {
            if (client.readyState === client.OPEN) {
                client.send(messageStr);
            }
        });
    }

    async start() {
        await this.initialize();
        
        this.server.listen(this.port, () => {
            logger.info(`🌐 RovoDev Web Server uruchomiony na porcie ${this.port}`);
            logger.info(`📱 Interfejs dostępny pod: http://localhost:${this.port}`);
        });
    }

    async stop() {
        logger.info('🛑 Zatrzymywanie RovoDev Web Server...');
        
        // Zamknij wszystkie połączenia WebSocket
        this.clients.forEach(client => {
            client.close();
        });
        
        // Zamknij serwer HTTP
        this.server.close();
        
        logger.info('✅ RovoDev Web Server zatrzymany');
    }
}

export { RovoDevWebServer };

// Uruchomienie serwera jeśli plik jest wykonywany bezpośrednio
if (import.meta.url === `file://${process.argv[1]}`) {
    const server = new RovoDevWebServer();
    
    // Obsługa sygnałów
    process.on('SIGINT', async () => {
        await server.stop();
        process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
        await server.stop();
        process.exit(0);
    });
    
    server.start().catch(error => {
        logger.error('❌ Błąd uruchamiania serwera:', error);
        process.exit(1);
    });
}