import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * System współpracy między agentami AI
 */
export class AgentCollaboration extends EventEmitter {
    constructor() {
        super();
        this.collaborationSessions = new Map();
        this.knowledgeExchange = new Map();
        this.peerReviews = new Map();
        this.consensusBuilding = new Map();
    }

    /**
     * Rozpoczyna sesję współpracy między agentami
     */
    async startCollaborationSession(sessionConfig) {
        const sessionId = uuidv4();
        const session = {
            id: sessionId,
            name: sessionConfig.name,
            participants: sessionConfig.participants, // ['planning', 'research', 'analysis', 'coding']
            objective: sessionConfig.objective,
            mode: sessionConfig.mode || 'sequential', // 'sequential', 'parallel', 'consensus'
            status: 'active',
            currentPhase: null,
            results: new Map(),
            exchanges: [],
            createdAt: new Date(),
            context: sessionConfig.context || {}
        };

        this.collaborationSessions.set(sessionId, session);
        this.emit('collaboration:started', sessionId, session);
        
        logger.info(`🤝 Rozpoczęto sesję współpracy: ${session.name} (${sessionId})`);
        return sessionId;
    }

    /**
     * Wymiana wiedzy między agentami
     */
    async exchangeKnowledge(fromAgent, toAgent, knowledge, sessionId = null) {
        const exchangeId = uuidv4();
        const exchange = {
            id: exchangeId,
            fromAgent,
            toAgent,
            knowledge,
            sessionId,
            timestamp: new Date(),
            type: 'knowledge_transfer',
            processed: false
        };

        this.knowledgeExchange.set(exchangeId, exchange);
        
        if (sessionId) {
            const session = this.collaborationSessions.get(sessionId);
            if (session) {
                session.exchanges.push(exchangeId);
            }
        }

        this.emit('knowledge:exchanged', exchangeId, exchange);
        logger.info(`📚 Wymiana wiedzy: ${fromAgent} → ${toAgent}`);
        
        return exchangeId;
    }

    /**
     * Peer review między agentami
     */
    async requestPeerReview(requestingAgent, reviewerAgent, artifact, criteria = {}) {
        const reviewId = uuidv4();
        const review = {
            id: reviewId,
            requestingAgent,
            reviewerAgent,
            artifact,
            criteria: {
                quality: criteria.quality || 'high',
                completeness: criteria.completeness || 'comprehensive',
                accuracy: criteria.accuracy || 'strict',
                ...criteria
            },
            status: 'pending',
            feedback: null,
            score: null,
            recommendations: [],
            createdAt: new Date(),
            completedAt: null
        };

        this.peerReviews.set(reviewId, review);
        this.emit('peer_review:requested', reviewId, review);
        
        logger.info(`👥 Żądanie peer review: ${requestingAgent} → ${reviewerAgent}`);
        return reviewId;
    }

    /**
     * Proces budowania konsensusu między agentami
     */
    async buildConsensus(topic, participants, proposals, votingRules = {}) {
        const consensusId = uuidv4();
        const consensus = {
            id: consensusId,
            topic,
            participants,
            proposals,
            votes: new Map(),
            status: 'voting',
            rules: {
                threshold: votingRules.threshold || 0.75, // 75% agreement
                allowAbstention: votingRules.allowAbstention || true,
                timeLimit: votingRules.timeLimit || 300000, // 5 minutes
                ...votingRules
            },
            result: null,
            createdAt: new Date(),
            deadline: new Date(Date.now() + (votingRules.timeLimit || 300000))
        };

        this.consensusBuilding.set(consensusId, consensus);
        this.emit('consensus:started', consensusId, consensus);
        
        logger.info(`🗳️ Rozpoczęto budowanie konsensusu: ${topic}`);
        
        // Auto-timeout
        setTimeout(() => {
            this.finalizeConsensus(consensusId);
        }, consensus.rules.timeLimit);

        return consensusId;
    }

    /**
     * Głosowanie w procesie konsensusu
     */
    async vote(consensusId, agentId, vote, reasoning = '') {
        const consensus = this.consensusBuilding.get(consensusId);
        if (!consensus || consensus.status !== 'voting') {
            throw new Error('Nieprawidłowy konsensus lub głosowanie zakończone');
        }

        if (!consensus.participants.includes(agentId)) {
            throw new Error('Agent nie jest uczestnikiem tego konsensusu');
        }

        consensus.votes.set(agentId, {
            vote, // 'approve', 'reject', 'abstain'
            reasoning,
            timestamp: new Date()
        });

        this.emit('consensus:vote', consensusId, agentId, vote);
        logger.info(`🗳️ Głos w konsensusie ${consensusId}: ${agentId} → ${vote}`);

        // Sprawdź czy można zakończyć głosowanie
        if (consensus.votes.size === consensus.participants.length) {
            await this.finalizeConsensus(consensusId);
        }
    }

    /**
     * Finalizacja procesu konsensusu
     */
    async finalizeConsensus(consensusId) {
        const consensus = this.consensusBuilding.get(consensusId);
        if (!consensus || consensus.status !== 'voting') return;

        const votes = Array.from(consensus.votes.values());
        const approvals = votes.filter(v => v.vote === 'approve').length;
        const rejections = votes.filter(v => v.vote === 'reject').length;
        const abstentions = votes.filter(v => v.vote === 'abstain').length;
        
        const totalVotes = approvals + rejections + (consensus.rules.allowAbstention ? 0 : abstentions);
        const approvalRate = totalVotes > 0 ? approvals / totalVotes : 0;

        const result = {
            decision: approvalRate >= consensus.rules.threshold ? 'approved' : 'rejected',
            approvalRate,
            votes: { approvals, rejections, abstentions },
            reasoning: this.synthesizeReasoning(votes),
            finalizedAt: new Date()
        };

        consensus.status = 'completed';
        consensus.result = result;

        this.emit('consensus:finalized', consensusId, consensus, result);
        logger.info(`✅ Konsensus finalizowany: ${consensus.topic} → ${result.decision}`);

        return result;
    }

    /**
     * Synteza uzasadnień z głosowania
     */
    synthesizeReasoning(votes) {
        const approvalReasons = votes.filter(v => v.vote === 'approve').map(v => v.reasoning);
        const rejectionReasons = votes.filter(v => v.vote === 'reject').map(v => v.reasoning);

        return {
            approvals: approvalReasons,
            rejections: rejectionReasons,
            summary: this.generateReasoningSummary(approvalReasons, rejectionReasons)
        };
    }

    generateReasoningSummary(approvals, rejections) {
        let summary = '';
        
        if (approvals.length > 0) {
            summary += `Argumenty za: ${approvals.join('; ')}. `;
        }
        
        if (rejections.length > 0) {
            summary += `Argumenty przeciw: ${rejections.join('; ')}.`;
        }

        return summary.trim();
    }

    /**
     * Koordynacja zadań między agentami
     */
    async coordinateTask(task, agentAssignments, dependencies = {}) {
        const coordinationId = uuidv4();
        const coordination = {
            id: coordinationId,
            task,
            assignments: agentAssignments, // { agentId: subtask }
            dependencies, // { subtaskId: [dependentSubtaskIds] }
            status: 'coordinating',
            results: new Map(),
            timeline: this.calculateCoordinationTimeline(agentAssignments, dependencies),
            createdAt: new Date()
        };

        // Rozpocznij koordynację
        this.emit('coordination:started', coordinationId, coordination);
        logger.info(`🎯 Rozpoczęto koordynację zadania: ${task.description}`);

        return coordinationId;
    }

    calculateCoordinationTimeline(assignments, dependencies) {
        // Algorytm planowania z uwzględnieniem zależności
        const timeline = [];
        const completed = new Set();
        const remaining = new Map(Object.entries(assignments));

        while (remaining.size > 0) {
            const ready = [];
            
            for (const [agentId, subtask] of remaining) {
                const deps = dependencies[subtask.id] || [];
                const canStart = deps.every(dep => completed.has(dep));
                
                if (canStart) {
                    ready.push({ agentId, subtask });
                }
            }

            if (ready.length === 0) {
                logger.warn('⚠️ Wykryto cykliczne zależności w koordynacji');
                break;
            }

            // Dodaj gotowe zadania do timeline
            ready.forEach(({ agentId, subtask }) => {
                timeline.push({
                    phase: timeline.length + 1,
                    agentId,
                    subtask,
                    estimatedStart: new Date(),
                    estimatedDuration: subtask.estimatedTime || 60
                });
                
                completed.add(subtask.id);
                remaining.delete(agentId);
            });
        }

        return timeline;
    }

    /**
     * Konflikt resolution między agentami
     */
    async resolveConflict(conflictDescription, involvedAgents, mediatorAgent = null) {
        const conflictId = uuidv4();
        const conflict = {
            id: conflictId,
            description: conflictDescription,
            involvedAgents,
            mediatorAgent,
            status: 'open',
            proposals: [],
            resolution: null,
            createdAt: new Date()
        };

        this.emit('conflict:detected', conflictId, conflict);
        logger.info(`⚡ Wykryto konflikt: ${conflictDescription}`);

        // Jeśli jest mediator, rozpocznij mediację
        if (mediatorAgent) {
            await this.startMediation(conflictId);
        }

        return conflictId;
    }

    async startMediation(conflictId) {
        const conflict = this.collaborationSessions.get(conflictId);
        if (!conflict) return;

        conflict.status = 'mediation';
        this.emit('mediation:started', conflictId, conflict);
        
        logger.info(`🕊️ Rozpoczęto mediację konfliktu: ${conflictId}`);
    }

    /**
     * Analiza wydajności współpracy
     */
    analyzeCollaborationEffectiveness(sessionId) {
        const session = this.collaborationSessions.get(sessionId);
        if (!session) return null;

        const metrics = {
            duration: session.completedAt ? session.completedAt - session.createdAt : Date.now() - session.createdAt,
            exchangeCount: session.exchanges.length,
            participantEngagement: this.calculateParticipantEngagement(session),
            consensusRate: this.calculateConsensusRate(session),
            conflictCount: this.getConflictCount(session),
            qualityScore: this.calculateQualityScore(session)
        };

        return {
            sessionId,
            metrics,
            recommendations: this.generateCollaborationRecommendations(metrics),
            analysis: this.generateCollaborationAnalysis(metrics)
        };
    }

    calculateParticipantEngagement(session) {
        const engagement = {};
        session.participants.forEach(agent => {
            const agentExchanges = session.exchanges.filter(exchangeId => {
                const exchange = this.knowledgeExchange.get(exchangeId);
                return exchange && (exchange.fromAgent === agent || exchange.toAgent === agent);
            });
            engagement[agent] = agentExchanges.length;
        });
        return engagement;
    }

    calculateConsensusRate(session) {
        const consensuses = Array.from(this.consensusBuilding.values())
            .filter(c => c.participants.some(p => session.participants.includes(p)));
        
        if (consensuses.length === 0) return 1;
        
        const successful = consensuses.filter(c => c.result?.decision === 'approved').length;
        return successful / consensuses.length;
    }

    getConflictCount(session) {
        // Implementacja zliczania konfliktów w sesji
        return 0; // Placeholder
    }

    calculateQualityScore(session) {
        // Złożony algorytm oceny jakości współpracy
        const baseScore = 0.7;
        const engagementBonus = Object.values(this.calculateParticipantEngagement(session))
            .reduce((sum, count) => sum + Math.min(count * 0.1, 0.2), 0);
        const consensusBonus = this.calculateConsensusRate(session) * 0.1;
        
        return Math.min(baseScore + engagementBonus + consensusBonus, 1.0);
    }

    generateCollaborationRecommendations(metrics) {
        const recommendations = [];
        
        if (metrics.consensusRate < 0.7) {
            recommendations.push('Popraw proces budowania konsensusu - rozważ lepsze przygotowanie propozycji');
        }
        
        if (metrics.exchangeCount < 5) {
            recommendations.push('Zwiększ wymianę wiedzy między agentami');
        }
        
        if (metrics.conflictCount > 2) {
            recommendations.push('Wprowadź lepsze mechanizmy prewencji konfliktów');
        }
        
        if (metrics.qualityScore < 0.8) {
            recommendations.push('Ogólna poprawa jakości współpracy - rozważ dodatkowe szkolenia agentów');
        }

        return recommendations;
    }

    generateCollaborationAnalysis(metrics) {
        return {
            efficiency: metrics.duration < 300000 ? 'high' : metrics.duration < 600000 ? 'medium' : 'low',
            engagement: Object.values(metrics.participantEngagement).reduce((a, b) => a + b, 0) > 10 ? 'high' : 'medium',
            consensus: metrics.consensusRate > 0.8 ? 'excellent' : metrics.consensusRate > 0.6 ? 'good' : 'needs_improvement',
            overall: metrics.qualityScore > 0.8 ? 'excellent' : metrics.qualityScore > 0.6 ? 'good' : 'needs_improvement'
        };
    }

    /**
     * Pobiera status wszystkich aktywnych współprac
     */
    getActiveCollaborations() {
        return Array.from(this.collaborationSessions.values())
            .filter(session => session.status === 'active');
    }

    /**
     * Pobiera historię współpracy dla agenta
     */
    getAgentCollaborationHistory(agentId) {
        const sessions = Array.from(this.collaborationSessions.values())
            .filter(session => session.participants.includes(agentId));
        
        const exchanges = Array.from(this.knowledgeExchange.values())
            .filter(exchange => exchange.fromAgent === agentId || exchange.toAgent === agentId);
        
        const reviews = Array.from(this.peerReviews.values())
            .filter(review => review.requestingAgent === agentId || review.reviewerAgent === agentId);

        return {
            sessions,
            exchanges,
            reviews,
            totalCollaborations: sessions.length,
            knowledgeExchanges: exchanges.length,
            peerReviews: reviews.length
        };
    }
}