import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * SuperAgent AI - Główny orkiestrator systemu wieloagentowego
 */
export class SuperAgent extends EventEmitter {
    constructor({ mcpManager, atlassianIntegration, rovoDevIntegration, agentRegistry, taskManager, agentWorkflow }) {
        super();
        this.mcpManager = mcpManager;
        this.atlassianIntegration = atlassianIntegration;
        this.rovoDevIntegration = rovoDevIntegration;
        this.agentRegistry = agentRegistry;
        this.taskManager = taskManager;
        this.agentWorkflow = agentWorkflow;
        this.activeTasks = new Map();
        this.taskHistory = [];
    }

    async initialize() {
        logger.info('🎯 Inicjalizacja SuperAgent...');
        
        // Inicjalizacja MCP
        await this.mcpManager.initialize();
        
        // Inicjalizacja integracji <PERSON>
        await this.atlassianIntegration.initialize();
        
        // Konfiguracja nasłuchiwania zdarzeń
        this.setupEventListeners();
        
        // Integracja z TaskManager
        this.setupTaskManagerIntegration();
        
        // Konfiguracja integracji RovoDev dla agentów
        this.setupRovoDevIntegration();
        
        logger.info('✅ SuperAgent zainicjalizowany');
    }

    setupEventListeners() {
        this.on('task:started', (taskId) => {
            logger.info(`📋 Rozpoczęto zadanie: ${taskId}`);
        });

        this.on('task:completed', (taskId, result) => {
            logger.info(`✅ Zakończono zadanie: ${taskId}`);
            this.taskHistory.push({ taskId, result, completedAt: new Date() });
        });

        this.on('agent:error', (agentId, error) => {
            logger.error(`❌ Błąd agenta ${agentId}:`, error);
        });
    }

    /**
     * Wykonuje zadanie przez orkiestrację agentów
     */
    async executeTask(task) {
        const taskId = task.id || uuidv4();
        this.emit('task:started', taskId);

        try {
            logger.info(`🎯 SuperAgent rozpoczyna orkiestrację zadania: ${taskId}`);
            
            // Faza 1: Planowanie (Planning Agent)
            const planningResult = await this.executePlanningPhase(task);
            logger.info('📋 Faza planowania zakończona:', planningResult);

            // Faza 2: Research (Research Agent)
            const researchResult = await this.executeResearchPhase(task, planningResult);
            logger.info('🔍 Faza research zakończona:', researchResult);

            // Faza 3: Analiza (Analysis Agent)
            const analysisResult = await this.executeAnalysisPhase(task, planningResult, researchResult);
            logger.info('📊 Faza analizy zakończona:', analysisResult);

            // Faza 4: Kodowanie (Coding Agent)
            const codingResult = await this.executeCodingPhase(task, analysisResult);
            logger.info('💻 Faza kodowania zakończona:', codingResult);

            const finalResult = {
                taskId,
                status: 'completed',
                phases: {
                    planning: planningResult,
                    research: researchResult,
                    analysis: analysisResult,
                    coding: codingResult
                },
                completedAt: new Date()
            };

            this.emit('task:completed', taskId, finalResult);
            return finalResult;

        } catch (error) {
            logger.error(`❌ Błąd podczas wykonywania zadania ${taskId}:`, error);
            this.emit('task:error', taskId, error);
            throw error;
        }
    }

    async executePlanningPhase(task) {
        const planningAgent = this.agentRegistry.getAgent('planning');
        return await planningAgent.execute({
            task,
            context: 'initial_planning'
        });
    }

    async executeResearchPhase(task, planningResult) {
        const researchAgent = this.agentRegistry.getAgent('research');
        return await researchAgent.execute({
            task,
            planningResult,
            context: 'research_and_analysis'
        });
    }

    async executeAnalysisPhase(task, planningResult, researchResult) {
        const analysisAgent = this.agentRegistry.getAgent('analysis');
        return await analysisAgent.execute({
            task,
            planningResult,
            researchResult,
            context: 'synthesis_and_preparation'
        });
    }

    async executeCodingPhase(task, analysisResult) {
        const codingAgent = this.agentRegistry.getAgent('coding');
        return await codingAgent.execute({
            task,
            analysisResult,
            context: 'implementation'
        });
    }

    setupTaskManagerIntegration() {
        if (!this.taskManager) return;

        // Nasłuchuj zdarzeń TaskManager i wykonuj zadania
        this.taskManager.on('task:started', async (taskId, task) => {
            try {
                logger.info(`🎯 SuperAgent przejmuje zadanie: ${taskId}`);
                const result = await this.executeTask(task);
                
                // Aktualizuj zadanie w TaskManager
                const managedTask = this.taskManager.getTask(taskId);
                if (managedTask) {
                    managedTask.status = 'completed';
                    managedTask.result = result;
                    managedTask.completedAt = new Date();
                }
            } catch (error) {
                logger.error(`❌ Błąd wykonania zadania ${taskId}:`, error);
                const managedTask = this.taskManager.getTask(taskId);
                if (managedTask) {
                    managedTask.status = 'failed';
                    managedTask.error = error;
                }
            }
        });
    }

    /**
     * Tworzy zadanie przez TaskManager
     */
    async createTask(taskData) {
        if (!this.taskManager) {
            // Fallback do bezpośredniego wykonania
            return await this.executeTask(taskData);
        }

        return await this.taskManager.createTask(taskData);
    }

    /**
     * Monitoruje status wszystkich aktywnych zadań
     */
    getSystemStatus() {
        const baseStatus = {
            activeTasks: this.activeTasks.size,
            completedTasks: this.taskHistory.length,
            agents: this.agentRegistry.getRegisteredAgents(),
            mcpStatus: this.mcpManager.getStatus(),
            atlassianStatus: this.atlassianIntegration.getStatus(),
            rovoDevStatus: this.rovoDevIntegration.getStatus()
        };

        if (this.taskManager) {
            baseStatus.taskManager = this.taskManager.getStatus();
        }

        if (this.agentWorkflow) {
            baseStatus.workflows = {
                active: this.agentWorkflow.getActiveWorkflows().length,
                templates: this.agentWorkflow.getWorkflowTemplates().length
            };
        }

        return baseStatus;
    }

    /**
     * Wykonuje workflow przez AgentWorkflow
     */
    async executeWorkflow(templateId, customization = {}) {
        if (!this.agentWorkflow) {
            throw new Error('AgentWorkflow nie jest skonfigurowany');
        }

        const workflowId = await this.agentWorkflow.createWorkflow(templateId, customization);
        return await this.agentWorkflow.executeWorkflow(workflowId, this.agentRegistry);
    }

    /**
     * Pobiera status workflow
     */
    getWorkflowStatus(workflowId) {
        if (!this.agentWorkflow) return null;
        return this.agentWorkflow.getWorkflowStatus(workflowId);
    }

    setupRovoDevIntegration() {
        if (!this.rovoDevIntegration) return;

        // Przekaż integrację RovoDev do agentów, które mogą z niej korzystać
        const codingAgent = this.agentRegistry.getAgent('coding');
        if (codingAgent && typeof codingAgent.setRovoDevIntegration === 'function') {
            codingAgent.setRovoDevIntegration(this.rovoDevIntegration);
            logger.info('🤖 RovoDev integration configured for CodingAgent');
        }

        const analysisAgent = this.agentRegistry.getAgent('analysis');
        if (analysisAgent && typeof analysisAgent.setRovoDevIntegration === 'function') {
            analysisAgent.setRovoDevIntegration(this.rovoDevIntegration);
            logger.info('🤖 RovoDev integration configured for AnalysisAgent');
        }
    }
}