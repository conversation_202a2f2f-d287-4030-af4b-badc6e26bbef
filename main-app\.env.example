# Konfiguracja serwerów MCP

# Context7 MCP Server (Upstash)
UPSTASH_REDIS_REST_URL=your_upstash_redis_url_here
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token_here

# 21st.dev MCP Server
TWENTYFIRST_API_KEY=your_21st_dev_api_key_here
TWENTYFIRST_ENDPOINT=https://api.21st.dev

# 21st.dev Magic MCP Server
TWENTYFIRST_MAGIC_API_KEY=a2963c042498ab5d207844614351b4db01af277ca1689ef267381d99dbd8eb93

# Atlassian Integration
ATLASSIAN_API_TOKEN=your_atlassian_token_here
ATLASSIAN_EMAIL=<EMAIL>
ATLASSIAN_DOMAIN=your-domain.atlassian.net

# Task Manager
TASK_PERSISTENCE_PATH=./data/tasks
MAX_CONCURRENT_TASKS=3
TASK_RETRY_ATTEMPTS=3

# GitHub Integration
GITHUB_PERSONAL_ACCESS_TOKEN=your_github_token_here

# Browser Search (Brave)
BRAVE_SEARCH_API_KEY=your_brave_search_api_key_here

# RovoDev Integration
ROVO_DEV_API_KEY=your_rovo_dev_api_key_here
ROVO_DEV_ENDPOINT=https://api.rovo.dev

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/system.log