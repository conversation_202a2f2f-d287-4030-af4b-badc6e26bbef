#!/usr/bin/env node

/**
 * Skrypt testowy dla Multi-Agent AI System
 */

import { SuperAgent } from './src/core/SuperAgent.js';
import { AgentRegistry } from './src/core/AgentRegistry.js';
import { TaskManager } from './src/core/TaskManager.js';
import { MCPManager } from './src/mcp/MCPManager.js';
import { AtlassianIntegration } from './src/integrations/AtlassianIntegration.js';
import { logger } from './src/utils/logger.js';
import dotenv from 'dotenv';

dotenv.config();

async function testSystem() {
    logger.info('🧪 Rozpoczęcie testów systemu Multi-Agent AI...');

    try {
        // Inicjalizacja komponentów
        const mcpManager = new MCPManager();
        const atlassianIntegration = new AtlassianIntegration();
        const agentRegistry = new AgentRegistry();
        const taskManager = new TaskManager({ persistencePath: './test-data/tasks' });
        
        const superAgent = new SuperAgent({
            mcpManager,
            atlassianIntegration,
            agentRegistry,
            taskManager
        });

        // Test 1: Inicjalizacja systemu
        logger.info('🔧 Test 1: Inicjalizacja systemu');
        await agentRegistry.registerAgents();
        await taskManager.initialize();
        await superAgent.initialize();
        logger.info('✅ Test 1 zakończony pomyślnie');

        // Test 2: Status systemu
        logger.info('🔧 Test 2: Sprawdzenie statusu systemu');
        const systemStatus = superAgent.getSystemStatus();
        logger.info('📊 Status systemu:', JSON.stringify(systemStatus, null, 2));
        logger.info('✅ Test 2 zakończony pomyślnie');

        // Test 3: Tworzenie zadań
        logger.info('🔧 Test 3: Tworzenie zadań');
        const testTasks = [
            {
                id: 'test-task-001',
                title: 'Test Task 1 - Podstawowy',
                description: 'Podstawowe zadanie testowe',
                priority: 'high',
                type: 'test'
            },
            {
                id: 'test-task-002',
                title: 'Test Task 2 - Z zależnością',
                description: 'Zadanie z zależnością od task-001',
                priority: 'medium',
                type: 'test',
                dependencies: ['test-task-001']
            },
            {
                id: 'test-task-003',
                title: 'Test Task 3 - Niski priorytet',
                description: 'Zadanie o niskim priorytecie',
                priority: 'low',
                type: 'test'
            }
        ];

        for (const taskData of testTasks) {
            const taskId = await superAgent.createTask(taskData);
            logger.info(`📋 Utworzono zadanie testowe: ${taskData.title} (${taskId})`);
        }
        logger.info('✅ Test 3 zakończony pomyślnie');

        // Test 4: Monitoring zadań
        logger.info('🔧 Test 4: Monitoring zadań');
        await new Promise(resolve => setTimeout(resolve, 2000)); // Czekaj 2 sekundy
        
        const taskStatus = taskManager.getStatus();
        logger.info('📊 Status zadań:', JSON.stringify(taskStatus, null, 2));
        logger.info('✅ Test 4 zakończony pomyślnie');

        // Test 5: Sprawdzenie narzędzi MCP
        logger.info('🔧 Test 5: Sprawdzenie narzędzi MCP');
        const mcpStatus = mcpManager.getStatus();
        logger.info('🔌 Status MCP:', JSON.stringify(mcpStatus, null, 2));
        
        const availableTools = mcpManager.getAllAvailableTools();
        logger.info(`🔧 Dostępne narzędzia MCP: ${availableTools.length}`);
        availableTools.forEach(tool => {
            logger.info(`  - ${tool.name} (${tool.serverName}): ${tool.description || 'Brak opisu'}`);
        });
        logger.info('✅ Test 5 zakończony pomyślnie');

        // Test 6: Test agentów
        logger.info('🔧 Test 6: Test agentów');
        const agents = agentRegistry.getRegisteredAgents();
        logger.info(`👥 Zarejestrowanych agentów: ${agents.length}`);
        agents.forEach(agent => {
            logger.info(`  - ${agent.name}: ${agent.description}`);
            logger.info(`    Możliwości: ${agent.capabilities.join(', ') || 'Brak'}`);
        });
        logger.info('✅ Test 6 zakończony pomyślnie');

        // Podsumowanie
        logger.info('🎉 Wszystkie testy zakończone pomyślnie!');
        logger.info('📊 Podsumowanie:');
        logger.info(`  - Agenci: ${agents.length}`);
        logger.info(`  - Serwery MCP: ${mcpStatus.totalServers} (połączone: ${mcpStatus.connectedServers})`);
        logger.info(`  - Narzędzia MCP: ${mcpStatus.totalTools}`);
        logger.info(`  - Zadania: ${taskStatus.total} (aktywne: ${taskStatus.running}, zakończone: ${taskStatus.completed})`);

        // Cleanup
        await taskManager.stop();
        await mcpManager.shutdown();

    } catch (error) {
        logger.error('❌ Błąd podczas testów:', error);
        process.exit(1);
    }
}

// Obsługa sygnałów
process.on('SIGINT', () => {
    logger.info('🛑 Przerwanie testów...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    logger.info('🛑 Zatrzymywanie testów...');
    process.exit(0);
});

testSystem();