import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Bazowa klasa dla wszystkich agentów
 */
export class BaseAgent extends EventEmitter {
    constructor({ id, name, description, capabilities = [] }) {
        super();
        this.id = id;
        this.name = name;
        this.description = description;
        this.capabilities = capabilities;
        this.status = 'idle';
        this.currentTask = null;
        this.taskHistory = [];
        this.mcpTools = new Map();
    }

    async initialize() {
        logger.info(`🔄 Inicjalizacja agenta: ${this.name}`);
        this.status = 'ready';
        this.emit('agent:initialized', this.id);
    }

    async execute(context) {
        const executionId = uuidv4();
        
        try {
            this.status = 'working';
            this.currentTask = { executionId, context, startedAt: new Date() };
            
            logger.info(`🎯 Agent ${this.name} rozpoczyna wykonanie zadania: ${executionId}`);
            this.emit('agent:task:started', this.id, executionId);

            // Główna logika wykonania - do implementacji w klasach pochodnych
            const result = await this.performTask(context);

            this.status = 'ready';
            this.currentTask = null;
            this.taskHistory.push({
                executionId,
                context,
                result,
                completedAt: new Date()
            });

            logger.info(`✅ Agent ${this.name} zakończył zadanie: ${executionId}`);
            this.emit('agent:task:completed', this.id, executionId, result);

            return result;

        } catch (error) {
            this.status = 'error';
            this.currentTask = null;
            
            logger.error(`❌ Błąd w agencie ${this.name}:`, error);
            this.emit('agent:task:error', this.id, executionId, error);
            
            throw error;
        }
    }

    /**
     * Główna metoda do implementacji w klasach pochodnych
     */
    async performTask(context) {
        throw new Error('Metoda performTask musi być zaimplementowana w klasie pochodnej');
    }

    /**
     * Rejestruje narzędzie MCP
     */
    registerMCPTool(name, tool) {
        this.mcpTools.set(name, tool);
        logger.info(`🔧 Agent ${this.name} zarejestrował narzędzie MCP: ${name}`);
    }

    /**
     * Używa narzędzia MCP
     */
    async useMCPTool(name, params) {
        const tool = this.mcpTools.get(name);
        if (!tool) {
            throw new Error(`Narzędzie MCP '${name}' nie zostało znalezione`);
        }

        logger.info(`🔧 Agent ${this.name} używa narzędzia MCP: ${name}`);
        return await tool.execute(params);
    }

    /**
     * Zwraca możliwości agenta
     */
    getCapabilities() {
        return [...this.capabilities];
    }

    /**
     * Zwraca status agenta
     */
    getStatus() {
        return {
            id: this.id,
            name: this.name,
            status: this.status,
            currentTask: this.currentTask,
            completedTasks: this.taskHistory.length,
            availableTools: Array.from(this.mcpTools.keys())
        };
    }

    /**
     * Loguje wiadomość z kontekstem agenta
     */
    log(level, message, ...args) {
        logger[level](`[${this.name}] ${message}`, ...args);
    }
}